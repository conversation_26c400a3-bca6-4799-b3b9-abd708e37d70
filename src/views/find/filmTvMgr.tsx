import React, { useState, useEffect, useMemo, memo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getTableList, setTableList } from '@app/action/tableList';
import { findApi as api } from '@app/api';
import { OrderColumn, Table } from '@components/common';
import { getCrumb, requirePerm, requirePerm4Function } from '@utils/utils';
import {
  Button,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Dropdown,
  Menu,
  Tooltip,
} from 'antd';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';
import moment from 'moment';
import { CommonObject } from '@app/types';
import { PermA, PermButton } from '@app/components/permItems';
import { setTableCache } from '@app/action/tableCache';
import ReactClipboard from 'react-clipboardjs-copy';
import AddFilmTvDrawer from './component/AddFilmTvDrawer';
import { setMenuHook } from '@app/utils/utils';
import { debounce, set } from 'lodash';
import { useStore } from 'react-redux';
// ✅ 添加图片预览功能
import { PhotoSlider } from 'react-photo-view';

interface FilterState {
  type: string | number;
  theme: string | number;
  show: string;
  multi_items: string;
  finish: string;
  search_type: number;
  keyword: string;
  source: string | number;
}

export default function FilmTvMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const location = useLocation();
  const match = useRouteMatch();

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);
  const { session } = useStore().getState();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  // Local state
  const [filter, setFilter] = useState<FilterState>({
    type: '',
    theme: '',
    show: '',
    multi_items: '',
    finish: '',
    search_type: 1,
    keyword: '',
    source: '',
  });
  const [search, setSearch] = useState<any>({
    search_type: 1,
    keyword: '',
  });
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editRecord, setEditRecord] = useState<any>(null);
  const [drawerKey, setDrawerKey] = useState(Date.now());
  const [isInitialized, setIsInitialized] = useState(false);
  // ✅ 添加图片预览状态
  const [imagePreview, setImagePreview] = useState({
    visible: false,
    imgs: [] as string[],
    index: 0,
  });

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      setFilter(tableCache?.filters);
      dispatch(setTableList(tableCache));
    } else {
      getData({ current: 1 });
    }

    setIsInitialized(true);
  }, []);

  // 获取数据
  const getData = (overlap: CommonObject = {}, f: any = filter) => {
    const { current, size } = tableList;
    dispatch(getTableList('getMediaList', 'list', { current, size, ...f, ...overlap }));
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    const f = {
      ...filter,
      [key]: value,
    };
    setFilter(f);

    getData({ current: 1 }, f);
  };

  // 处理搜索 - 添加防抖
  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const f = {
        ...filter,
        ...search,
      };
      if (!f.keyword) {
        delete f.keyword;
        delete f.search_type;
      }

      setFilter(f);

      getData({ current: 1 }, f);
    }
  };

  // 处理排序
  const handleSort = (record: any, pos: number) => {
    let newPos = pos;
    const posChange = (value: number | undefined) => {
      newPos = value || pos;
    };

    Modal.confirm({
      title: <p>排序：《{record.title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            placeholder="请输入数字"
            min={1}
            defaultValue={pos}
            onChange={posChange}
            style={{ width: '200px', marginLeft: '8px' }}
            precision={0}
          />
        </div>
      ),
      onOk: () => {
        if (!newPos || newPos < 1) {
          message.error('请填写有效的序号');
          return Promise.reject();
        }

        setLoading(true);
        return api
          .updateMediaOrder({
            id: record.id,
            sort_flag: 2, // 指定位置
            number: newPos,
          })
          .then(() => {
            message.success('操作成功');
            getData();
          })
          .catch(() => {})
          .finally(() => {
            setLoading(false);
          });
      },
      okText: '确定',
      cancelText: '取消',
    });
  };

  // 处理状态变更
  const handleStatusChange = (record: any) => {
    const isOnline = record.show === true;
    Modal.confirm({
      title: isOnline ? '下架后，在所有页面都不再展示，也不能播放' : '确定上架？',
      onOk: () => {
        setLoading(true);
        api
          .updateMediaOnline({
            id: record.id,
            online: !isOnline,
          })
          .then(() => {
            message.success('操作成功');
            getData();
            setLoading(false);
          })
          .catch(() => {
            setLoading(false);
          });
      },
    });
  };

  // 处理删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '删除影视内容将同步删除底下所有资源',
      onOk: () => {
        setLoading(true);
        api
          .deleteMedia({
            id: record.id,
          })
          .then(() => {
            message.success('删除成功');
            getData();
            setLoading(false);
          })
          .catch(() => {
            setLoading(false);
          });
      },
    });
  };

  // 处理编辑
  const handleEdit = (record?: any) => {
    setDrawerVisible(true);
    setEditRecord(record || null);
    setDrawerKey(Date.now());
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
    setEditRecord(null);
  };

  // 处理抽屉确认
  const handleDrawerOk = () => {
    // API调用已在AddFilmTvDrawer组件内部处理
    // 这里只需要刷新列表
    getData();
  };

  // 处理管理资源
  const handleManageResource = (record: any) => {
    dispatch(
      setTableCache({
        ...tableList,
        filters: filter,
        beforeRoute: match.path,
      })
    );

    // 跳转到资源管理列表页
    history.push(
      `/view/media_content_list?filmId=${record.id}&filmName=${encodeURIComponent(record.title)}`
    );
  };

  // 处理排序操作
  const order = (record: any, current: Number, type: Number) => {
    setLoading(true);
    api
      .updateMediaOrder({
        id: record.id,
        sort_flag: type === -1 ? 0 : 1, // 0: 向上, 1: 向下
      })
      .then(() => {
        message.success('操作成功');
        getData();
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 检查是否有筛选条件或搜索关键词
  const hasFiltersOrSearch = (): boolean => {
    return Boolean(
      filter.type ||
        filter.theme ||
        filter.show ||
        filter.multi_items ||
        filter.finish ||
        filter.source ||
        (filter.keyword && filter.keyword.trim())
    );
  };

  // 获取列配置
  const columns = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const currentSeq = getSeq(i); // 当前项目的全局序号

        // 排序禁用条件：
        // 1. 记录状态为未展示 (record.show === false)
        // 2. 有筛选条件或搜索关键词时
        const isDisabled = record.show === false || hasFiltersOrSearch();

        return (
          <OrderColumn
            pos={getSeq(i)}
            start={1}
            end={allData.on_show_count}
            perm="media:edit"
            disable={isDisabled}
            onUp={() => order(record, currentSeq, -1)}
            onDown={() => order(record, currentSeq, 1)}
          />
        );
      },
      width: 80,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 60,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '影视名称',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text: string, record: any) => (
        <PermA perm="media:edit" onClick={() => handleEdit(record)}>
          {text}
        </PermA>
      ),
    },
    {
      title: '竖封面图',
      dataIndex: 'list_pic_h',
      key: 'list_pic_h',
      width: 100,
      render: (text: string, record: any) => (
        <img
          src={text || record.list_pic_h}
          alt="封面"
          style={{ width: 60, height: 80, objectFit: 'cover', display: 'block', cursor: 'pointer' }}
          onClick={() => {
            const imageUrl = text || record.list_pic_h;
            if (imageUrl) {
              setImagePreview({
                visible: true,
                imgs: [imageUrl],
                index: 0,
              });
            }
          }}
        />
      ),
    },
    {
      title: '分类',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (text: string | number) => {
        const categoryMap: { [key: string]: string } = {
          '1': '电视剧',
          '2': '电影',
          '3': '短剧',
          '4': '纪录片',
          '5': '综艺',
        };
        return categoryMap[String(text)] || '-';
      },
    },
    {
      title: (
        <div>
          资源数&nbsp;
          <Tooltip title="当前影视下已上传的资源总数" placement="top">
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'item_count',
      key: 'item_count',
      width: 80,
      render: (text: number, record: any) => (
        <a onClick={() => handleManageResource(record)}>{text || 0}</a>
      ),
    },
    {
      title: '类型',
      dataIndex: 'theme_names',
      key: 'theme_names',
      width: 80,
      render: (text: string, record: any) => {
        if (record.themes) {
          const themeMap: { [key: string]: string } = {
            '1': '爱情',
            '2': '战争',
            '3': '动作',
            '4': '都市',
            '5': '公益',
            '6': '家庭',
            '7': '金融',
            '8': '历史',
            '9': '励志',
            '10': '文化',
            '11': '喜剧',
            '12': '乡村',
            '13': '悬疑',
          };

          const themeIds = record.themes.split(',');
          const themeNames = themeIds
            .map((id: string) => themeMap[id.trim()])
            .filter((name: string) => name)
            .join(',');

          return themeNames || '-';
        }
        return text || '-';
      },
    },
    {
      title: (
        <div>
          播放量&nbsp;
          <Tooltip
            title="有分集的影视，统计的是所有资源播放量总和（包括下架状态的）"
            placement="top"
          >
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'read_count',
      key: 'read_count',
      width: 100,
      render: (text: number) => (text || 0).toLocaleString(),
    },
    {
      title: '状态',
      dataIndex: 'show',
      key: 'show',
      width: 80,
      render: (text: boolean) => (text ? '展示中' : '未展示'),
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      key: 'updated_by',
      width: 100,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 160,
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 100,
      render: (text: any, record: any, i: number) => {
        const menu = (
          <Menu>
            {requirePerm4Function(
              session,
              'media:edit'
            )(<Menu.Item onClick={() => handleEdit(record)}>编辑信息</Menu.Item>)}
            {requirePerm4Function(
              session,
              'media:view'
            )(<Menu.Item onClick={() => handleManageResource(record)}>管理资源</Menu.Item>)}
            {record.show !== false &&
              requirePerm4Function(
                session,
                'media:edit'
              )(<Menu.Item onClick={() => handleSort(record, getSeq(i))}>排序</Menu.Item>)}
            {requirePerm4Function(
              session,
              'media:online'
            )(
              <Menu.Item onClick={() => handleStatusChange(record)}>
                {record.show === true ? '下架' : '上架'}
              </Menu.Item>
            )}
            {requirePerm4Function(
              session,
              'media:edit'
            )(<Menu.Item onClick={() => handleDelete(record)}>删除</Menu.Item>)}
            <Menu.Item>
              <ReactClipboard
                action="copy"
                text={record.url || ''}
                onSuccess={() => message.success('链接已复制')}
                onError={() => message.error('复制失败')}
              >
                <span>复制分享链接</span>
              </ReactClipboard>
            </Menu.Item>
            <Menu.Item>
              <ReactClipboard
                action="copy"
                text={`/pages/videoplayer/index?id=${record.article_id}`}
                onSuccess={() => message.success('链接已复制')}
                onError={() => message.error('复制失败')}
              >
                <span>复制小程序路由</span>
              </ReactClipboard>
            </Menu.Item>
          </Menu>
        );

        return (
          <Dropdown overlay={menu}>
            <a className="ant-dropdown-link">
              操作 <Icon type="down" />
            </a>
          </Dropdown>
        );
      },
    },
  ];

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="media:edit" onClick={() => handleEdit()} style={{ marginRight: 8 }}>
            <Icon type="plus-circle" /> 添加影视内容
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item>
                <Select
                  value={filter.type}
                  onChange={(value) => handleFilterChange('type', value)}
                  style={{ width: 120 }}
                  placeholder="影视类型"
                >
                  <Select.Option value="">全部分类</Select.Option>
                  <Select.Option value={1}>电视剧</Select.Option>
                  <Select.Option value={2}>电影</Select.Option>
                  <Select.Option value={3}>短剧</Select.Option>
                  <Select.Option value={4}>纪录片</Select.Option>
                  <Select.Option value={5}>综艺</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.theme}
                  onChange={(value) => handleFilterChange('theme', value)}
                  style={{ width: 120 }}
                  placeholder="题材类型"
                >
                  <Select.Option value="">全部类型</Select.Option>
                  <Select.Option value="1">爱情</Select.Option>
                  <Select.Option value="2">战争</Select.Option>
                  <Select.Option value="3">动作</Select.Option>
                  <Select.Option value="4">都市</Select.Option>
                  <Select.Option value="5">公益</Select.Option>
                  <Select.Option value="6">家庭</Select.Option>
                  <Select.Option value="7">金融</Select.Option>
                  <Select.Option value="8">历史</Select.Option>
                  <Select.Option value="9">励志</Select.Option>
                  <Select.Option value="10">文化</Select.Option>
                  <Select.Option value="11">喜剧</Select.Option>
                  <Select.Option value="12">乡村</Select.Option>
                  <Select.Option value="13">悬疑</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.show}
                  onChange={(value) => handleFilterChange('show', value)}
                  style={{ width: 120 }}
                  placeholder="展示状态"
                >
                  <Select.Option value="">展示状态</Select.Option>
                  <Select.Option value="true">展示中</Select.Option>
                  <Select.Option value="false">未展示</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.multi_items}
                  onChange={(value) => handleFilterChange('multi_items', value)}
                  style={{ width: 120 }}
                  placeholder="是否分集"
                >
                  <Select.Option value="">是否分集</Select.Option>
                  <Select.Option value="true">分集</Select.Option>
                  <Select.Option value="false">不分集</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.finish}
                  onChange={(value) => handleFilterChange('finish', value)}
                  style={{ width: 120 }}
                  placeholder="是否完结"
                >
                  <Select.Option value="">是否完结</Select.Option>
                  <Select.Option value="true">已完结</Select.Option>
                  <Select.Option value="false">更新中</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.source}
                  onChange={(value) => handleFilterChange('source', value)}
                  style={{ width: 120 }}
                  placeholder="数据来源"
                >
                  <Select.Option value="">全部来源</Select.Option>
                  <Select.Option value={0}>潮新闻</Select.Option>
                  <Select.Option value={1}>火把</Select.Option>
                  <Select.Option value={2}>学习强国</Select.Option>
                  <Select.Option value={3}>红果短剧</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Select
                value={search.search_type}
                onChange={(value) => setSearch({ ...search, search_type: value })}
                style={{ width: 100, marginRight: 8 }}
              >
                <Select.Option value={1}>影视名称</Select.Option>
                <Select.Option value={2}>影视ID</Select.Option>
              </Select>
              <Input
                value={search.keyword}
                onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
                placeholder="请输入搜索内容"
                style={{ width: 160, marginRight: 8 }}
                onKeyPress={handleKey}
              />
              <Button onClick={() => handleKey({ which: 13 })}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          func="getMediaList"
          index="list"
          rowKey="id"
          filter={filter}
          columns={columns}
          pagination={true}
          tableProps={{ scroll: { x: 1640 } }}
        />

        <AddFilmTvDrawer
          visible={drawerVisible}
          record={editRecord}
          onClose={handleDrawerClose}
          onOk={handleDrawerOk}
          skey={drawerKey}
        />

        {/* ✅ 添加图片预览组件 */}
        <PhotoSlider
          maskOpacity={0.5}
          images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
          visible={imagePreview.visible}
          onClose={() =>
            setImagePreview({
              ...imagePreview,
              visible: false,
            })
          }
          index={imagePreview.index}
          onIndexChange={(index) =>
            setImagePreview({
              ...imagePreview,
              index,
            })
          }
        />
      </div>
    </>
  );
}
