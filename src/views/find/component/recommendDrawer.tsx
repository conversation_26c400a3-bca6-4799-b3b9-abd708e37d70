import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Form,
  Button,
  message,
  Input,
  Row,
  Col,
  Radio,
  InputNumber,
  Select,
  Spin,
  Modal,
  Icon,
  Tooltip,
  TimePicker,
  Table,
} from 'antd';
import { Drawer } from '@app/components/common';
import { findApi, searchApi } from '@app/api';
import { FormComponentProps } from 'antd/es/form';
import debounce from 'lodash/debounce';
import moment from 'moment';
import ImageUploader from '@app/components/common/imageUploader';
import { useDispatch, useSelector } from 'react-redux';
import { setConfig } from '@app/action/config';

interface RecommendDrawerProps extends FormComponentProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  editRecord?: any;
}

interface MediaItem {
  id: string;
  title: string;
  brief_summary?: string;
  recommend_number?: number;
  recommend_start_time?: string;
  recommend_end_time?: string;
  type?: string;
  recommend_time?: string;
}

interface ColumnType {
  title: string;
  key: string;
  dataIndex?: keyof MediaItem;
  render?: (text: any, record: MediaItem, index: number) => React.ReactNode;
  width?: number;
}

// 添加弹窗相关接口
interface RecommendTextModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (text: string) => void;
  initialValue: string;
}

interface RecommendPlayModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (number: number, startTime?: string, endTime?: string) => void;
  initialNumber: number;
  initialStartTime?: string;
  initialEndTime?: string;
}

// 推荐语弹窗组件
const RecommendTextModal: React.FC<RecommendTextModalProps> = ({
  visible,
  onCancel,
  onOk,
  initialValue,
}) => {
  const [text, setText] = useState(initialValue || '');

  useEffect(() => {
    if (visible) {
      setText(initialValue || '');
    }
  }, [visible, initialValue]);

  const handleOk = () => {
    onOk(text);
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Icon type="question-circle" style={{ color: '#faad14', marginRight: 8 }} />
          <span>推荐语</span>
        </div>
      }
      visible={visible}
      onCancel={onCancel}
      onOk={handleOk}
      okText="确定"
      cancelText="取消"
      width={450}
    >
      <Input.TextArea
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="最多输入20字"
        maxLength={20}
        style={{ resize: 'none', height: 100 }}
      />
      <div style={{ textAlign: 'right', marginTop: 8 }}>{text.length}/20</div>
    </Modal>
  );
};

// 推荐播放片段弹窗组件
const RecommendPlayModal: React.FC<RecommendPlayModalProps> = ({
  visible,
  onCancel,
  onOk,
  initialNumber = 1,
  initialStartTime,
  initialEndTime,
}) => {
  const [number, setNumber] = useState(initialNumber);
  const [startTime, setStartTime] = useState<moment.Moment | undefined>(
    initialStartTime ? moment(initialStartTime, 'HH:mm:ss') : undefined
  );
  const [endTime, setEndTime] = useState<moment.Moment | undefined>(
    initialEndTime ? moment(initialEndTime, 'HH:mm:ss') : undefined
  );

  useEffect(() => {
    if (visible) {
      setNumber(initialNumber);
      setStartTime(initialStartTime ? moment(initialStartTime, 'HH:mm:ss') : undefined);
      setEndTime(initialEndTime ? moment(initialEndTime, 'HH:mm:ss') : undefined);
    }
  }, [visible, initialNumber, initialStartTime, initialEndTime]);

  const handleOk = () => {
    onOk(
      number,
      startTime ? startTime.format('HH:mm:ss') : undefined,
      endTime ? endTime.format('HH:mm:ss') : undefined
    );
  };

  const handleStartTimeChange = (time: moment.Moment | null) => {
    setStartTime(time || undefined);
  };

  const handleEndTimeChange = (time: moment.Moment | null) => {
    setEndTime(time || undefined);
  };

  return (
    <Modal
      title="推荐播放片段"
      visible={visible}
      onCancel={onCancel}
      onOk={handleOk}
      okText="确定"
      cancelText="取消"
      width={500}
    >
      <div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <span style={{ marginRight: 8 }}>第</span>
          <InputNumber
            min={1}
            max={9999}
            precision={0}
            value={number}
            onChange={(value) => setNumber(value as number)}
            style={{ width: 100 }}
            placeholder="选填"
          />
          <span style={{ marginLeft: 8, marginRight: 8 }}>个资源</span>
        
        </div>

        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: 8 }}>开始时间-结束时间</span>
          <div style={{ display: 'flex', flex: 1 }}>
            <TimePicker
              placeholder="开始时间"
              style={{ flex: 1 }}
              format="HH:mm:ss"
              value={startTime}
              onChange={handleStartTimeChange}
              defaultOpenValue={moment('00:00:00', 'HH:mm:ss')}
            />
            <span style={{ margin: '0 8px', lineHeight: '32px' }}>至</span>
            <TimePicker
              placeholder="结束时间"
              style={{ flex: 1 }}
              format="HH:mm:ss"
              value={endTime}
              onChange={handleEndTimeChange}
              defaultOpenValue={startTime || moment('00:00:00', 'HH:mm:ss')}
            />
          </div>
        </div>
        <div style={{ marginTop: 16, color: '#999', fontSize: 12 }}>
          资源不设置，默认第1个；时间不设置，默认从头播到尾；设置数据错误（资源时间与实际不符），按默认规则处理
        </div>
      </div>
    </Modal>
  );
};

const RecommendDrawerForm: React.FC<RecommendDrawerProps> = (props) => {
  const { visible, onClose, onSuccess, editRecord, form } = props;
  const { getFieldDecorator, validateFields, resetFields, setFieldsValue, getFieldValue } = form;
  const dispatch = useDispatch();
  const loading = useSelector((state: any) => state.config?.mLoading || false);

  // 状态管理
  const [isEdit, setIsEdit] = useState(false);
  const [refType, setRefType] = useState<number>(45); // 45-组合影视推荐位 46-单条影视推荐位
  const [mediaList, setMediaList] = useState<MediaItem[]>([]);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searching, setSearching] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<any>(null);
  const [drawerKey] = useState(() => Date.now());
  const [recommendDetail, setRecommendDetail] = useState<any>(null); // 添加推荐位详情

  // 添加弹窗状态
  const [recommendTextModalVisible, setRecommendTextModalVisible] = useState(false);
  const [recommendTextModalIndex, setRecommendTextModalIndex] = useState<number>(-1);
  const [recommendPlayModalVisible, setRecommendPlayModalVisible] = useState(false);
  const [recommendPlayModalIndex, setRecommendPlayModalIndex] = useState<number>(-1);

  // 重置状态函数
  const resetState = useCallback(() => {
    resetFields();
    setMediaList([]);
    setRefType(45);
    setIsEdit(false);
    setSearchResults([]);
    setSelectedMedia(null);
    dispatch(setConfig({ mLoading: false }));
    // 重置弹窗状态
    setRecommendTextModalVisible(false);
    setRecommendTextModalIndex(-1);
    setRecommendPlayModalVisible(false);
    setRecommendPlayModalIndex(-1);
  }, [resetFields, dispatch]);

  // 关闭抽屉
  const handleClose = useCallback(() => {
    resetState();
    onClose();
  }, [resetState, onClose]);

  // 获取推荐位数据
  const fetchRecommendData = useCallback(async () => {
    if (!editRecord) return;

    try {
      dispatch(setConfig({ mLoading: true }));
      const res = await findApi.getRecommendPositionDetail({ id: editRecord.id });
      if (res.data) {
        const data = res.data as any;
        const recommend = data.recommend;
        setRecommendDetail(recommend); // 保存推荐位详情
        setRefType(recommend.ref_type || 45);
        
        // 只设置一定会渲染的基础字段
        setFieldsValue({
          ref_type: recommend.ref_type || 45,
          title: recommend.title,
          position: recommend.position,
          title_style: recommend.title_style,
          jump_enabled: recommend.jump_enabled,
        });

        if (data.medias && data.medias.length > 0) {
          setMediaList(
            data.medias.map((item: any) => ({
              id: item.media_id,
              title: item.title,
              brief_summary: item.brief_summary,
              recommend_number: item.recommend_number || 1,
              recommend_start_time: item.recommend_start_time,
              recommend_end_time: item.recommend_end_time,
              type: item.type,
            }))
          );
        }
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      dispatch(setConfig({ mLoading: false }));
    }
  }, [editRecord, setFieldsValue, dispatch]);

  // 初始化数据
  useEffect(() => {
    if (visible) {
      setIsEdit(!!editRecord);
      if (editRecord) {
        fetchRecommendData();
      }
    }
  }, [visible, editRecord, fetchRecommendData]);

  // 搜索影视
  const handleMediaSearch = useCallback((value: string) => {
    if (!value) {
      setSearchResults([]);
      return;
    }

    setSearching(true);
    searchApi
      .searchFilmTv({
        keyword: value,
      })
      .then((res: any) => {
        const list = res.data?.list || [];
        setSearchResults(list);
      })
      .catch(() => {
        setSearchResults([]);
      })
      .finally(() => {
        setSearching(false);
      });
  }, []);

  // 防抖处理搜索
  const debouncedSearch = useMemo(() => debounce(handleMediaSearch, 500), [handleMediaSearch]);

  // 选择影视
  const handleMediaChange = useCallback(
    (value: any) => {
      if (!value) return;

      const selectedItem = searchResults.find((item) => item.id === value);
      if (!selectedItem) return;

      // ✅ 检查是否已存在
      const existingMedia = mediaList.find((item) => item.id === selectedItem.id);
      if (existingMedia) {
        message.error('该影视内容已添加');
        return;
      }

      // ✅ 单条类型只能添加一条
      const currentRefType = getFieldValue('ref_type');
      if (currentRefType === 46 && mediaList.length >= 1) {
        message.error('只支持添加1条数据');
        return;
      }

      // ✅ 直接添加新媒体到表格
      const newMedia: MediaItem = {
        id: selectedItem.id,
        title: selectedItem.title,
        brief_summary: selectedItem.brief_summary || '',
        recommend_number: 1,
        type: selectedItem.type,
        recommend_start_time: selectedItem.recommend_start_time || '',
        recommend_end_time: selectedItem.recommend_end_time || '',
      };

      setMediaList((prevList) => [...prevList, newMedia]);

      // ✅ 清空选择并重置搜索结果
      setSelectedMedia(null);
      setSearchResults([]);

      // ✅ 设置表单字段值，标记已添加关联影视
      setFieldsValue({ has_media: true });
    },
    [searchResults, mediaList, getFieldValue, setFieldsValue]
  );

  // 添加选中的影视 - 保留函数但不再使用
  const handleAddMedia = useCallback(() => {
    if (!selectedMedia) {
      message.warning('请先选择一个影视');
      return;
    }

    // 检查是否已存在
    const existingMedia = mediaList.find((item) => item.id === selectedMedia.id);
    if (existingMedia) {
      message.error('该影视内容已添加');
      return;
    }

    // 单条类型只能添加一条
    const currentRefType = getFieldValue('ref_type');
    if (currentRefType === 46 && mediaList.length >= 1) {
      message.error('只支持添加1条数据');
      return;
    }

    // 添加新媒体
    const newMedia: MediaItem = {
      id: selectedMedia.id,
      title: selectedMedia.title,
      brief_summary: selectedMedia.brief_summary || '',
      recommend_number: 1,
      type: selectedMedia.type,
      recommend_start_time: selectedMedia.recommend_start_time || '',
      recommend_end_time: selectedMedia.recommend_end_time || '',
    };

    setMediaList((prevList) => [...prevList, newMedia]);

    // 清空选择
    setSelectedMedia(null);

    // 设置表单字段值，标记已添加关联影视
    setFieldsValue({ has_media: true });
  }, [selectedMedia, mediaList, getFieldValue, setFieldsValue]);

  // 更新推荐语
  const updateRecommendText = useCallback((index: number, text: string) => {
    setMediaList((prevList) => {
      const newList = [...prevList];
      newList[index] = { ...newList[index], brief_summary: text };
      return newList;
    });
  }, []);

  // 更新推荐片段
  const updateRecommendNumber = useCallback((index: number, number: number) => {
    setMediaList((prevList) => {
      const newList = [...prevList];
      newList[index] = { ...newList[index], recommend_number: number };
      return newList;
    });
  }, []);

  // 删除影视
  const handleRemoveMedia = useCallback(
    (index: number) => {
      setMediaList((prevList) => {
        const newList = [...prevList];
        newList.splice(index, 1);
        if (newList.length === 0) {
          setFieldsValue({ has_media: false });
        }
        return newList;
      });
    },
    [setFieldsValue]
  );

  // 上移
  const handleMoveUp = useCallback((index: number) => {
    if (index === 0) return;
    setMediaList((prevList) => {
      const newList = [...prevList];
      [newList[index], newList[index - 1]] = [newList[index - 1], newList[index]];
      return newList;
    });
  }, []);

  // 下移
  const handleMoveDown = useCallback((index: number) => {
    setMediaList((prevList) => {
      if (index === prevList.length - 1) return prevList;
      const newList = [...prevList];
      [newList[index], newList[index + 1]] = [newList[index + 1], newList[index]];
      return newList;
    });
  }, []);

  // 处理跳转启用状态变化
  const handleJumpEnabledChange = useCallback((e: any) => {
    const jumpEnabled = e.target.value;
    // 如果选择无跳转，清空跳转地址
    if (!jumpEnabled) {
      setFieldsValue({ jump_model_url: '' });
    }
  }, [setFieldsValue]);

  // 处理类型变更
  const handleRefTypeChange = useCallback((e: any) => {
    setRefType(e.target.value);
    // 清空已选择的媒体列表
    setMediaList([]);
    // 如果切换到单条影视推荐位，重置跳转相关字段
    if (e.target.value === 46) {
      setFieldsValue({
        jump_enabled: false,
        jump_model_url: undefined,
      });
    }
  }, [setFieldsValue]);

  // 处理标题样式变更
  const handleTitleStyleChange = useCallback((e: any) => {
    const titleStyle = e.target.value;
    // 当标题样式切换时，清空标题图片
    setFieldsValue({ title_pic: undefined });
  }, [setFieldsValue]);

  // 渲染表格单元格
  const renderCell = useCallback((item: MediaItem, column: ColumnType, index: number) => {
    if (column.render) {
      return column.render(column.dataIndex ? item[column.dataIndex] : null, item, index);
    }
    return column.dataIndex ? item[column.dataIndex] : null;
  }, []);

  // 打开推荐语弹窗
  const handleOpenRecommendTextModal = useCallback((index: number) => {
    setRecommendTextModalIndex(index);
    setRecommendTextModalVisible(true);
  }, []);

  // 关闭推荐语弹窗
  const handleCloseRecommendTextModal = useCallback(() => {
    setRecommendTextModalVisible(false);
    setRecommendTextModalIndex(-1);
  }, []);

  // 确认推荐语修改
  const handleConfirmRecommendText = useCallback(
    (text: string) => {
      if (recommendTextModalIndex >= 0 && recommendTextModalIndex < mediaList.length) {
        updateRecommendText(recommendTextModalIndex, text);
      }
      handleCloseRecommendTextModal();
    },
    [recommendTextModalIndex, mediaList.length, updateRecommendText, handleCloseRecommendTextModal]
  );

  // 打开推荐播放片段弹窗
  const handleOpenRecommendPlayModal = useCallback((index: number) => {
    setRecommendPlayModalIndex(index);
    setRecommendPlayModalVisible(true);
  }, []);

  // 关闭推荐播放片段弹窗
  const handleCloseRecommendPlayModal = useCallback(() => {
    setRecommendPlayModalVisible(false);
    setRecommendPlayModalIndex(-1);
  }, []);

  // 确认推荐播放片段修改
  const handleConfirmRecommendPlay = useCallback(
    (number: number, startTime?: string, endTime?: string) => {
      if (recommendPlayModalIndex >= 0 && recommendPlayModalIndex < mediaList.length) {
        const newList = [...mediaList];
        newList[recommendPlayModalIndex] = {
          ...newList[recommendPlayModalIndex],
          recommend_number: number,
          recommend_start_time: startTime,
          recommend_end_time: endTime,
          recommend_time: startTime && endTime ? `${startTime}-${endTime}` : undefined,
        };
        setMediaList(newList);
      }
      handleCloseRecommendPlayModal();
    },
    [recommendPlayModalIndex, mediaList, handleCloseRecommendPlayModal]
  );

  // 表格列配置
  const tableColumns = useMemo<ColumnType[]>(() => {
    if (refType === 45) {
      // 组合推荐位表格列
      return [
        {
          title: '排序',
          key: 'sort',
          width: 80,
          render: (_: any, __: MediaItem, index: number) => (
            <div>
              <Button
                icon="arrow-up"
                size="small"
                style={{ marginRight: 4 }}
                disabled={index === 0}
                onClick={() => handleMoveUp(index)}
              />
              <Button
                icon="arrow-down"
                size="small"
                disabled={index === mediaList.length - 1}
                onClick={() => handleMoveDown(index)}
              />
            </div>
          ),
        },
        {
          title: '潮新闻ID',
          dataIndex: 'id',
          key: 'id',
          width: 120,
        },
        {
          title: '影视名称',
          dataIndex: 'title',
          key: 'title',
          width: 120,
        },
        {
          title: '分类',
          dataIndex: 'type',
          key: 'type',
          width: 80,
          render: (text: string | number) => {
            const categoryMap: { [key: string]: string } = {
              '1': '电视剧',
              '2': '电影',
              '3': '短剧',
              '4': '纪录片',
              '5': '综艺',
            };
            return categoryMap[String(text)] || '-';
          },
        },
        {
          title: '推荐语',
          key: 'brief_summary',
          render: (_: any, record: MediaItem, index: number) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>{record.brief_summary}</span>
              <Button
                type="link"
                size="small"
                style={{ padding: '0 8px', color: '#1890ff' }}
                onClick={() => handleOpenRecommendTextModal(index)}
              >
                修改
              </Button>
            </div>
          ),
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          render: (_: any, __: MediaItem, index: number) => (
            <Button
              type="link"
              style={{ color: '#1890ff', padding: 0 }}
              onClick={() => handleRemoveMedia(index)}
            >
              删除
            </Button>
          ),
        },
      ];
    } else {
      // 单条推荐位表格列
      return [
        {
          title: '潮新闻ID',
          dataIndex: 'id',
          key: 'id',
          width: 120,
        },
        {
          title: '影视名称',
          dataIndex: 'title',
          key: 'title',
          width: 120,
        },
        {
          title: '分类',
          dataIndex: 'type',
          key: 'type',
          width: 80,
          render: (text: string | number) => {
            const categoryMap: { [key: string]: string } = {
              '1': '电视剧',
              '2': '电影',
              '3': '短剧',
              '4': '纪录片',
              '5': '综艺',
            };
            return categoryMap[String(text)] || '-';
          },
        },
        {
          title: '推荐语',
          key: 'brief_summary',
          render: (_: any, record: MediaItem, index: number) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>{record.brief_summary}</span>
              <Button
                type="link"
                size="small"
                style={{ padding: '0 8px', color: '#1890ff' }}
                onClick={() => handleOpenRecommendTextModal(index)}
              >
                修改
              </Button>
            </div>
          ),
        },
        // {
        //   title: '推荐播放片段',
        //   key: 'recommend_time',
        //   render: (_: any, record: MediaItem, index: number) => (
        //     <div style={{ display: 'flex', alignItems: 'center' }}>
        //       <span>
        //         {record.recommend_number ? `第${record.recommend_number}个资源` : ''}
        //         {record.recommend_start_time && record.recommend_end_time
        //           ? ` ${record.recommend_start_time}-${record.recommend_end_time}`
        //           : record.recommend_time || ''}
        //       </span>
        //       <Button
        //         type="link"
        //         size="small"
        //         style={{ padding: '0 8px', color: '#1890ff' }}
        //         onClick={() => handleOpenRecommendPlayModal(index)}
        //       >
        //         修改
        //       </Button>
        //     </div>
        //   ),
        // },
        {
          title: '操作',
          key: 'action',
          width: 80,
          render: (_: any, __: MediaItem, index: number) => (
            <Button
              type="link"
              style={{ color: '#1890ff', padding: 0 }}
              onClick={() => handleRemoveMedia(index)}
            >
              删除
            </Button>
          ),
        },
      ];
    }
  }, [
    refType,
    mediaList.length,
    handleMoveUp,
    handleMoveDown,
    handleRemoveMedia,
    handleOpenRecommendTextModal,
    handleOpenRecommendPlayModal,
  ]);

  // 提交表单
  const handleSubmit = useCallback(() => {
    validateFields(async (err, values) => {
      if (err) return;

      // 组合类型至少需要3条
      if (values.ref_type === 45 && mediaList.length < 4) {
        message.error('请至少选择4条关联影视');
        setFieldsValue({ has_media: false });
        return;
      }

      // 单条类型必须有1条
      if (values.ref_type === 46 && mediaList.length < 1) {
        message.error('请选择关联影视');
        setFieldsValue({ has_media: false });
        return;
      }

      // 处理推荐语，去除首尾空格
      const processedMediaList = mediaList.map((item) => ({
        ...item,
        brief_summary: item.brief_summary ? item.brief_summary.trim() : '',
      }));

      // 生成ref_ids和ref_extensions
      const ref_ids = processedMediaList.map((item) => item.id).join(',');
      const ref_extensions = JSON.stringify(
        processedMediaList.map((item) => ({
          media_id: parseInt(item.id),
          article_id: parseInt(item.id),
          title: item.title,
          type: item.type,
          brief_summary: item.brief_summary || '',
          recommend_number: item.recommend_number || 1,
          recommend_start_time: item.recommend_start_time || '',
          recommend_end_time: item.recommend_end_time || '',
        }))
      );

      // 移除undefined字段的函数
      const removeUndefinedFields = (obj: any) => {
        const result: any = {};
        Object.keys(obj).forEach((key) => {
          if (obj[key] !== undefined) {
            result[key] = obj[key];
          }
        });
        return result;
      };

      const params = {
        ref_type: values.ref_type,
        title: values.title,
        position: values.position,
        title_style: values.ref_type === 45 ? values.title_style : undefined,
        jump_enabled: values.ref_type === 45 ? values.jump_enabled : undefined,
        jump_model_url:
          values.ref_type === 45 && values.jump_enabled ? values.jump_model_url : undefined,
        title_pic: values.title_pic,
        ref_ids,
        ref_extensions,
        channel_id: 'b2ff6033c3c07b5876emedia',
        article_category: 0,
      };

      // 移除undefined字段
      const cleanedParams = removeUndefinedFields(params);

      try {
        dispatch(setConfig({ mLoading: true }));
        if (editRecord) {
          await findApi.updateRecommendPosition({ ...cleanedParams, id: editRecord.id });
          message.success('更新成功');
        } else {
          await findApi.createRecommendPosition(cleanedParams);
          message.success('创建成功');
        }
        // 操作成功后只触发成功事件，不传递数据
        onSuccess && onSuccess();
        handleClose();
      } catch (error) {
        message.error('操作失败');
      } finally {
        dispatch(setConfig({ mLoading: false }));
      }
    });
  }, [validateFields, mediaList, editRecord, onSuccess, handleClose, dispatch]);

  const titleStyleTip = (desc: string, picName: string, width: number, height: number) => {
    return (
      <div>
        <div style={{ marginBottom: 5 }} dangerouslySetInnerHTML={{ __html: desc }}></div>
        <img
          src={`/assets/${picName}.png`}
          width={width}
          height={height}
          alt={`${picName} 样式预览`}
        />
      </div>
    );
  };
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  return (
    <>
      <Drawer
        visible={visible}
        title={`${editRecord ? '编辑' : '创建'}影视推荐位`}
        onClose={handleClose}
        onOk={handleSubmit}
        skey={drawerKey}
        width={850}
        config={{ mLoading: loading }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={handleClose} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              保存
            </Button>
          </div>
        }
      >
        <Form {...formLayout}>
          {/* 隐藏字段，用于验证是否添加了关联影视 */}
          {getFieldDecorator('has_media', {
            initialValue: mediaList.length > 0,
            rules: [{ required: true, message: '请添加关联影视' }],
          })(<Input type="hidden" />)}

          <Form.Item label="推荐位类型" colon={false}>
            {getFieldDecorator('ref_type', {
              initialValue: refType,
            })(
              <Radio.Group onChange={handleRefTypeChange} disabled={isEdit}>
                <Radio value={45}>组合影视推荐位</Radio>
                <Radio value={46}>单条影视推荐位</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          <Form.Item label="推荐位名称" colon={false}>
            {getFieldDecorator('title', {
              rules: [
                { required: true, message: '请输入推荐位名称' },
                { max: 15, message: '不超过15个字' },
              ],
            })(<Input placeholder="请输入名称，不超过15个字" />)}
          </Form.Item>

          {getFieldValue('ref_type') === 45 && (
            <Form.Item label=" " colon={false}>
              {getFieldDecorator('title_style', {
                initialValue: -1,
              })(
                <Radio.Group onChange={handleTitleStyleChange}>
                  <Radio style={{ fontSize: 13 }} value={-1}>
                    不显示标题
                  </Radio>
                  <Radio style={{ fontSize: 13 }} value={0}>
                    标题为默认样式 &nbsp;
                    <Tooltip
                      title={titleStyleTip(
                        '图标默认为蓝色竖条标题为<br/>推荐位名称',
                        'recommend_title1',
                        200,
                        49
                      )}
                    >
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                  {/* <Radio style={{ fontSize: 13 }} value={1}>
                    标题为图片样式 &nbsp;
                    <Tooltip
                      title={titleStyleTip('标题为一整张图片', 'recommend_title2', 150, 49)}
                    >
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio> */}
                  <Radio style={{ fontSize: 13 }} value={2}>
                    标题为图文样式 &nbsp;
                    <Tooltip
                      title={titleStyleTip(
                        '标题为一张自定义图标+推荐位名称（仅上传一张图标即可）',
                        'recommend_title3',
                        200,
                        92
                      )}
                    >
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
          )}
          {getFieldValue('ref_type') === 45 && getFieldValue('title_style') === 1 && (
            <Form.Item
              style={{ marginLeft: 120, marginTop: -20 }}
              extra="支持上传jpg,jpeg,png,gif图片格式, 比例未限制, 建议高度75px"
            >
              {getFieldDecorator('title_pic', {
                initialValue: recommendDetail?.title_pic,
                rules: [
                  {
                    required: true,
                    message: '请上传图片',
                  },
                ],
              })(<ImageUploader isCutting />)}
            </Form.Item>
          )}
          {getFieldValue('ref_type') === 45 && getFieldValue('title_style') === 2 && (
            <Form.Item
              style={{ marginLeft: 120, marginTop: -20 }}
              extra="支持上传jpg,jpeg,png,gif图片格式,比例为1:1,建议尺寸60px*60px"
            >
              {getFieldDecorator('title_pic', {
                initialValue: recommendDetail?.title_pic,
                rules: [
                  {
                    required: true,
                    message: '请上传图片',
                  },
                ],
              })(<ImageUploader ratio={1 / 1} />)}
            </Form.Item>
          )}

          {getFieldValue('ref_type') === 45 && (
            <Form.Item label="更多跳转" colon={false}>
              {getFieldDecorator('jump_enabled', {
                initialValue: false,
                rules: [{ required: true, message: '请选择是否有更多跳转' }],
              })(
                <Radio.Group onChange={handleJumpEnabledChange}>
                  <Radio value={false}>无跳转</Radio>
                  <Radio value={true}>有跳转</Radio>
                </Radio.Group>
              )}
            </Form.Item>
          )}
          {getFieldValue('ref_type') === 45 && getFieldValue('jump_enabled') === true && (
            <Form.Item label="跳转地址" colon={false}>
                {getFieldDecorator('jump_model_url', {
                  initialValue: recommendDetail?.jump_model_url || '',
                  rules: [
                    { required: true, message: '请输入跳转链接' },
                  ],
                })(<Input placeholder="请输入要跳转的链接" style={{ width: 300 }} />)}
            </Form.Item>
          )}
          <Form.Item label="显示位置" colon={false}>
            {getFieldDecorator('position', {
              rules: [
                { required: true, message: '请输入显示位置' },
                { pattern: /^[1-9]\d*$/, message: '请输入1-500的正整数' },
              ],
            })(
              <InputNumber
                min={1}
                max={500}
                placeholder="请输入在列表中显示的位置序号，1-500正整数"
                style={{ width: '70%' }}
                disabled={isEdit}
              />
            )}
            <Tooltip
              title="固定显示在影视专区首页列表的指定位置，与系统计算的影视内容混排"
              placement="right"
            >
              <Icon type="question-circle" style={{ marginLeft: 8 }} />
            </Tooltip>
          </Form.Item>
          <Form.Item label="关联影视：">
            <Row gutter={16}>
              <Col span={24}>
                <Select
                  showSearch
                  placeholder="输入ID或标题关联内容"
                  notFoundContent={searching ? <Spin size="small" /> : null}
                  filterOption={false}
                  onSearch={debouncedSearch}
                  onChange={handleMediaChange}
                  value={selectedMedia?.id || undefined}
                  style={{ width: '100%' }}
                  suffixIcon={<Icon type="caret-down" />}
                >
                  {searchResults.map((item) => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.id} - {item.title}
                    </Select.Option>
                  ))}
                </Select>
              </Col>
            </Row>
          </Form.Item>

          <div className="media-list">
            <Table
              dataSource={mediaList}
              rowKey="id"
              pagination={false}
              columns={tableColumns}
              bordered
            />
            {refType === 45 && mediaList.length < 4 && (
              <div style={{ color: '#ff4d4f', marginTop: 8, fontSize: '12px' }}>
                为保证客户端显示效果，关联影视数不能少于4条
              </div>
            )}
          </div>
        </Form>
      </Drawer>

      {/* 推荐语弹窗 */}
      <RecommendTextModal
        visible={recommendTextModalVisible}
        onCancel={handleCloseRecommendTextModal}
        onOk={handleConfirmRecommendText}
        initialValue={
          recommendTextModalIndex >= 0 && recommendTextModalIndex < mediaList.length
            ? mediaList[recommendTextModalIndex].brief_summary || ''
            : ''
        }
      />

      {/* 推荐播放片段弹窗 */}
      <RecommendPlayModal
        visible={recommendPlayModalVisible}
        onCancel={handleCloseRecommendPlayModal}
        onOk={handleConfirmRecommendPlay}
        initialNumber={
          recommendPlayModalIndex >= 0 && recommendPlayModalIndex < mediaList.length
            ? mediaList[recommendPlayModalIndex].recommend_number || 1
            : 1
        }
        initialStartTime={
          recommendPlayModalIndex >= 0 && recommendPlayModalIndex < mediaList.length
            ? mediaList[recommendPlayModalIndex].recommend_start_time
            : undefined
        }
        initialEndTime={
          recommendPlayModalIndex >= 0 && recommendPlayModalIndex < mediaList.length
            ? mediaList[recommendPlayModalIndex].recommend_end_time
            : undefined
        }
      />
    </>
  );
};

const RecommendDrawer = Form.create<RecommendDrawerProps>({ name: 'recommendDrawer' })(
  RecommendDrawerForm
);

export default RecommendDrawer;