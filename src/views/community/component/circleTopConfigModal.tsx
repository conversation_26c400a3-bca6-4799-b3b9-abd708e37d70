import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Form, Icon, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { communityApi, opApi } from '@app/api';
import { Drawer, SearchAndInput } from '@app/components/common';
import { useDispatch, useStore } from 'react-redux';
import { max } from 'lodash';
import { setConfig } from '@app/action/config';
import { PermButton } from '@app/components/permItems';
import moment from 'moment';

const CircleTopConfigModal = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;
  const store = useStore();
  const { permissions } = store.getState().session;

  const column = [
    {
      title: '潮新闻ID',
      key: 'id',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '标题',
      key: 'title',
      dataIndex: 'list_title',
    },
    {
      title: '所属圈子',
      key: 'circle_name',
      dataIndex: 'circle_name',
      width: 100,
    },
    {
      title: '类型',
      key: 'doc_type',
      dataIndex: 'doc_type',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <span>
            {record.doc_type == 10 ? '小视频' : record.doc_type == 12 ? '短图文' : '长文章'}
          </span>
        );
      },
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        const parmas = {
          ref_extensions: values.article_list?.map((v: any) => v.id)?.join(',') || '',
        };
        communityApi
          .saveCircleTopConfig(parmas)
          .then((res: any) => {
            message.success('操作成功');
            dispatch(setConfig({ mLoading: false }));
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  return (
    <Drawer
      skey={props.key}
      visible={props.visible}
      onClose={props.onCancel}
      title={
        <>
          推荐圈子
          <span
            style={{
              color: '#999',
              fontSize: 12,
            }}
          >
            显示在V7.x及以后版本的潮圈-发现模块
          </span>
        </>
      }
      footer={
        <>
          <span style={{ marginRight: 10 }}>
            最后操作人：{props.record?.detail.updated_by || ''}&nbsp;&nbsp; 最后操作时间：
            {moment(props.record?.detail.updated_at).format('YYYY-MM-DD HH:mm:ss')}
          </span>
          <>
            <Button style={{ marginRight: 8 }} onClick={props.onCancel}>
              取消
            </Button>
            <PermButton
              type="primary"
              perm={'circle:top_config_save'}
              onClick={handleSubmit}
              loading={loading}
            >
              确定
            </PermButton>
          </>
        </>
      }
      onOk={handleSubmit}
      maskClosable={false}
      okPerm="circle:top_config_save"
    >
      <Form {...formLayout} onSubmit={handleSubmit}>
        <Form.Item label="选择内容" required>
          {getFieldDecorator('article_list', {
            initialValue: props.record?.article_list || [],
            rules: [
              {
                max: 10,
                message: '最多可添加10篇内容',
                type: 'array',
              },
            ],
          })(
            <NewNewsSearchAndInput
              draggable={true}
              max={10}
              func="listArticleRecommendSearch"
              columns={column}
              placeholder="输入ID或标题关联内容"
              body={{ doc_types: '10,12,13' }}
              order={true}
              addOnTop={true}
              searchKey="keyword"
              indexKey="article_list"
            />
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'CircleTopConfigModal' })(
  forwardRef<any, any>(CircleTopConfigModal)
);
