import { Button, Modal, message, Spin, Form, Input, Radio } from 'antd';
import React from 'react';
import connectSession from '@app/utils/connectSession';
import { CommonObject } from '@app/types';
import { releaseListApi } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import { FormComponentProps } from 'antd/es/form';

const { TextArea } = Input;

interface IAIMusicModalProps extends FormComponentProps {
  visible: boolean;
  onClose: () => void;
  onConfirm?: (musicUrl: string, musicName?: string) => void; // ✅ 修改接口，支持传递音乐名称
  config?: CommonObject;
  record?: any; // 接收外部传入的record对象
}

interface IAIMusicModalState {
  currentStep: 'form' | 'audio'; // 当前步骤：表单填写 | 音频播放
  musicUrl: string; // 生成的音乐链接
  musicName: string; // ✅ 生成的音乐名称
  isGeneratingDesc: boolean; // 是否正在生成描述
  isGeneratingMusic: boolean; // 是否正在生成音乐
  formData: any; // 🔧 保存表单数据，确保步骤切换时数据不丢失
  musicDescValue: string; // 🔧 音乐描述值，用于实时控制按钮状态
}

@connectSession
class AIMusicModal extends React.Component<IAIMusicModalProps, IAIMusicModalState> {
  audioRef: React.RefObject<HTMLAudioElement>;

  constructor(props: IAIMusicModalProps) {
    super(props);
    this.state = {
      currentStep: 'form',
      musicUrl: '',
      musicName: '', // ✅ 初始化音乐名称
      isGeneratingDesc: false,
      isGeneratingMusic: false,
      formData: {}, // 🔧 初始化表单数据存储
      musicDescValue: '', // 🔧 初始化音乐描述值
    };
    this.audioRef = React.createRef<HTMLAudioElement>();
  }

  componentDidMount() {
    // 🔧 弹窗显示时初始化数据
    if (this.props.visible) {
      this.handleGenerateDescription();
    }
  }

  componentDidUpdate(prevProps: IAIMusicModalProps) {
    // 🔧 弹窗从隐藏变为显示时，重新初始化数据
    if (!prevProps.visible && this.props.visible) {
      this.handleGenerateDescription();
    }
  }

  // ✅ 获取AI描述接口
  handleGenerateDescription = () => {
    const { record } = this.props;
    if (!record || !record.id) {
      message.error('缺少稿件信息');
      return;
    }

    this.setState({ isGeneratingDesc: true });

    // 调用AI提示词生成接口
    releaseListApi.getAiTips({ article_id: record.id })
      .then((res: any) => {
        if (res.data) {
          // 获取到描述后，填入表单
          if (this.props.form) {
            const musicDesc = res.data.music_desc;
            this.props.form.setFieldsValue({
              music_style: res.data.music_style_value, // 填入音乐风格，默认为企业风格
              music_desc: musicDesc // 填入音乐描述
            });
            // 🔧 同时更新状态中的音乐描述值
            this.setState({
              isGeneratingDesc: false,
              musicDescValue: musicDesc
            });
          } else {
            this.setState({ isGeneratingDesc: false });
          }
        }
      })
      .catch((error) => {
        // 接口调用异常，静默处理，不显示错误提示
        message.error('网络遇到问题，请点击重试')
        this.setState({ isGeneratingDesc: false });
      })
  };

  // ✅ 重新生成描述
  handleRegenerateDescription = () => {
    this.handleGenerateDescription();
  };

  // ✅ 生成音乐
  handleGenerateMusic = () => {
    const { record } = this.props;
    if (!this.props.form) return;

    if (!record || !record.id) {
      message.error('缺少稿件信息');
      return;
    }

    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        // 🔧 保存表单数据到state中，确保数据不丢失
        this.setState({
          isGeneratingMusic: true,
          formData: values  // 保存当前表单数据
        });

        // 调用AI配乐生成接口
        releaseListApi.getAIMusicDescription({
          article_id: record.id,
          music_style: values.music_style,
          music_desc: values.music_desc
        })
          .then((res: any) => {
            if (res.data) {
              this.setState({
                musicUrl: res.data.music_url || '',
                musicName: res.data.music_name || 'AI生成音乐', // ✅ 处理音乐名称
                currentStep: 'audio',
                isGeneratingMusic: false,
                formData: values  // 保存表单数据
              });
            }
          })
          .catch((error) => {
            // 接口调用异常，静默处理，不显示错误提示
             message.error('网络遇到问题，请点击重试')
            this.setState({ isGeneratingMusic: false });
          })
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  // ✅ 返回上一步 - 保持表单内容和音乐链接
  handleBackToForm = () => {
    this.setState({
      currentStep: 'form'
      // 🔧 不清空musicUrl，保持音乐链接
    });

    // 🔧 恢复表单数据
    if (this.props.form && this.state.formData) {
      console.log('恢复表单数据:', this.state.formData); // 调试信息
      // 使用setTimeout确保在组件重新渲染后设置表单值
      setTimeout(() => {
        this.props.form.setFieldsValue(this.state.formData);
        console.log('表单数据已恢复'); // 调试信息
      }, 0);
    }
  };

  // ✅ 使用该音乐
  handleConfirmMusic = () => {
    const { musicUrl, musicName } = this.state;
    if (this.props.onConfirm) {
      this.props.onConfirm(musicUrl, musicName); // ✅ 传递音乐链接和名称
    }
    this.handleCloseModal();
  };

  // ✅ 关闭弹窗并清空所有数据
  handleCloseModal = () => {
    // 🔧 重置组件状态
    this.setState({
      currentStep: 'form',
      musicUrl: '',
      musicName: '', // ✅ 清空音乐名称
      isGeneratingDesc: false,
      isGeneratingMusic: false,
      formData: {}, // 🔧 清空保存的表单数据
      musicDescValue: '', // 🔧 清空音乐描述值
    });

    // 🔧 清空表单数据
    if (this.props.form) {
      this.props.form.resetFields();
    }

    // 🔧 停止音频播放
    if (this.audioRef.current) {
      this.audioRef.current.pause();
      this.audioRef.current.currentTime = 0;
    }

    // 🔧 调用外部关闭回调
    this.props.onClose();
  };

  // ✅ 渲染表单步骤
  // 🔧 使用Antd Form.create()装饰器，表单数据会自动保持，支持步骤间切换时的数据回显
  renderFormStep = () => {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 }
    };

    return (
      <Form {...formLayout}>
        <Form.Item label="音乐风格" required>
          {getFieldDecorator('music_style', {
            initialValue: 'corporate', // 默认选择企业风格
            rules: [
              {
                required: true,
                message: '请选择音乐风格',
              },
            ],
          })(
            <Radio.Group>
              <Radio value="corporate">企业</Radio>
              <Radio value="dance/edm">舞曲/电子</Radio>
              <Radio value="orchestral">管弦乐</Radio>
              <Radio value="chill out">放松</Radio>
              <Radio value="rock">摇滚</Radio>
              <Radio value="hip hop">嘻哈</Radio>
              <Radio value="folk">民谣</Radio>
              <Radio value="funk">放克</Radio>
              <Radio value="ambient">氛围</Radio>
              <Radio value="holiday">节日</Radio>
              <Radio value="jazz">爵士</Radio>
              <Radio value="kids">儿童</Radio>
              <Radio value="world">世界音乐</Radio>
              <Radio value="travel">旅行</Radio>
              <Radio value="commercial">商业用途</Radio>
              <Radio value="advertising">广告</Radio>
              <Radio value="driving">驾车</Radio>
              <Radio value="cinematic">电影感</Radio>
              <Radio value="upbeat">欢快</Radio>
              <Radio value="epic">史诗</Radio>
              <Radio value="inspiring">励志</Radio>
              <Radio value="business">商务</Radio>
              <Radio value="video game">电子游戏</Radio>
              <Radio value="dark">黑暗</Radio>
              <Radio value="pop">流行</Radio>
              <Radio value="trailer">预告片</Radio>
              <Radio value="modern">现代</Radio>
              <Radio value="electronic">电子</Radio>
              <Radio value="documentary">纪录片</Radio>
              <Radio value="soundtrack">原声带</Radio>
              <Radio value="fashion">时尚</Radio>
              <Radio value="acoustic">原声</Radio>
              <Radio value="movie">电影</Radio>
              <Radio value="tv">电视</Radio>
              <Radio value="high tech">高科技</Radio>
              <Radio value="industrial">工业</Radio>
              <Radio value="dance">舞蹈</Radio>
              <Radio value="video">视频</Radio>
              <Radio value="vlog">Vlog</Radio>
              <Radio value="marketing">市场营销</Radio>
              <Radio value="game">游戏</Radio>
              <Radio value="radio">广播</Radio>
              <Radio value="promotional">宣传</Radio>
              <Radio value="sports">体育</Radio>
              <Radio value="party">派对</Radio>
              <Radio value="summer">夏日</Radio>
              <Radio value="beauty">美妆</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="音乐描述" required>
          {getFieldDecorator('music_desc', {
            rules: [
              {
                required: true,
                message: '请输入音乐描述',
              },
            ],
          })(
            <TextArea
              placeholder="这里是AI为您生成的音乐描述文案，也可以手动修改"
              rows={4}
              maxLength={200}
              onChange={(e) => {
                this.setState({ musicDescValue: e.target.value });
              }}
            />
          )}
          <div style={{ textAlign: 'right', marginTop: 5, color: '#999' }}>
            最多200字
          </div>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
          <Button
            type="default"
            onClick={this.handleRegenerateDescription}
            loading={this.state.isGeneratingDesc}
            style={{ marginRight: 10 }}
          >
            重新生成描述
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // ✅ 渲染音频播放步骤
  renderAudioStep = () => {
    const { musicUrl, musicName } = this.state;

    const audioPlayerStyle = {
      borderRadius: 4,
      padding: 15,
      marginBottom: 20,
      border: '1px solid #d9d9d9'
    };

    return (
      <div>
        {/* ✅ 显示音乐名称 */}
        <div style={{ marginBottom: 20 }}>
          <div style={{ marginBottom: 10, fontWeight: 'bold' }}>AI生成：</div>
          <div style={{ fontSize: 14, color: '#333', marginBottom: 15 }}>
            {musicName || '这里是AI生成音乐名称'}
          </div>
          <div style={audioPlayerStyle}>
            <audio
              ref={this.audioRef}
              src={musicUrl}
              style={{ width: '100%' }}
              controls
            />
          </div>
        </div>
      </div>
    );
  };

  // ✅ 渲染底部按钮 - 适配Modal footer
  renderFooterButtons = () => {
    const { currentStep, isGeneratingMusic, musicDescValue } = this.state;

    if (currentStep === 'form') {
      // 检查音乐描述是否为空
      const formValues = this.props.form?.getFieldsValue() || {};
      const musicDesc = formValues.music_desc || musicDescValue || '';
      const isDescEmpty = !musicDesc.trim();

      return [
        <Button key="cancel" onClick={this.handleCloseModal}>
          取消
        </Button>,
        <Button
          key="generate"
          type="primary"
          onClick={this.handleGenerateMusic}
          loading={isGeneratingMusic}
          disabled={isDescEmpty} // 当音乐描述为空时禁用按钮
        >
          生成音乐
        </Button>
      ];
    }

    if (currentStep === 'audio') {
      return [
        <Button key="back" onClick={this.handleBackToForm}>
          上一步
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={this.handleConfirmMusic}
        >
          使用该音乐
        </Button>
      ];
    }

    return null;
  };

  render() {
    const { visible } = this.props;
    const { currentStep, musicUrl, isGeneratingDesc, isGeneratingMusic } = this.state;
    const loading = this.props.config ? this.props.config.mLoading : false;

    const modalContentStyle = {
      padding: 20,
      maxHeight: '60vh',
      overflowY: 'auto' as 'auto'
    };

    return (
      <Modal
        visible={visible}
        title="AI音乐"
        width={800}
        destroyOnClose={true}
        maskClosable={false}
        onCancel={this.handleCloseModal}
        footer={this.renderFooterButtons()}
        bodyStyle={{ padding: 0, position: 'relative' }}
      >
        {(loading || isGeneratingDesc || isGeneratingMusic) && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000
          }}>
            <Spin size="large" />
            <div style={{ marginTop: 16, fontSize: 14, color: '#666' }}>
              {isGeneratingDesc && 'AI正在理解图文内容，预计等待1分钟...'}
              {isGeneratingMusic && '预计需等待1分钟左右，请勿关闭页面...'}
              {loading && '处理中...'}
            </div>
          </div>
        )}

        <div style={modalContentStyle}>
          {currentStep === 'form' && this.renderFormStep()}
          {currentStep === 'audio' && this.renderAudioStep()}
        </div>
      </Modal>
    );
  }
}

export default Form.create<IAIMusicModalProps>()(AIMusicModal);
