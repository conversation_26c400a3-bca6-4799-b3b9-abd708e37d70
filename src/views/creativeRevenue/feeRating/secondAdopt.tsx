import React, { useState, useEffect, memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getTableList, setTableList } from '@app/action/tableList';
import { Table } from '@components/common';
import SortableColumn from '@components/common/sortableColumn';
import { getCrumb, UserDetail, requirePerm, requirePerm4Function } from '@utils/utils';
import {
  Divider,
  Button,
  Col,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Dropdown,
  Menu,
  Timeline,
  Tooltip,
} from 'antd';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';
import moment from 'moment';
import { CommonObject } from '@app/types';
import { PermA, PermButton } from '@app/components/permItems';
import { setMenuHook } from '@app/utils/utils';
import { debounce } from 'lodash';
import { userApi, communityApi, creativeRevenueApi, releaseList<PERSON>pi } from '@app/api';
import { setConfig } from '@app/action/config';
import { setTableCache } from '@app/action/tableCache';
import FeeRatingModal from './components/FeeRatingModal';
import { useStore } from 'react-redux';
import '@app/views/news/index.scss';
import PreviewMCN from '@components/common/previewMCN';
import SourceInfoModal from './components/SourceInfoModal';

interface FilterState {
  fee_type: number | string;
  fee_grade: number | string;
  author_type: number | string;
  search_type: number;
  keyword: string;
  sort_by?: number;
  sort_asc?: number;
  spread_index: string;
}

export default function SecondAdopt(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const location = useLocation();
  const match = useRouteMatch();

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);
  const { session } = useStore().getState();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);

  // Local state
  const [filter, setFilter] = useState<FilterState>({
    fee_type: '',
    fee_grade: '',
    author_type: '',
    search_type: 1,
    keyword: '',
    spread_index: '',
  });

  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [ratingModalVisible, setRatingModalVisible] = useState(false);
  const [ratingRecord, setRatingRecord] = useState<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 搜索状态
  const [search, setSearch] = useState<{
    keyword: string;
    author_id?: string;
  }>({
    keyword: '',
  });

  // 用户详情弹窗
  const [userDetailModal, setUserDetailModal] = useState({
    visible: false,
    key: Date.now(),
    detail: null,
  });

  // 操作日志弹窗
  const [operateLog, setOperateLog] = useState<{
    visible: boolean;
    logs: any[];
  }>({
    visible: false,
    logs: [],
  });

  // 作者搜索建议
  const [authorSuggestions, setAuthorSuggestions] = useState<any[]>([]);

  // 上榜详情弹窗
  const [rankDetailModal, setRankDetailModal] = useState<{
    visible: boolean;
    data: any;
    loading: boolean;
  }>({
    visible: false,
    data: null,
    loading: false,
  });

  // 稿件预览弹窗
  const [preview, setPreview] = useState<{
    visible: boolean;
    data: any;
    skey: number;
  }>({
    visible: false,
    data: null,
    skey: Date.now(),
  });

  // 源稿信息弹窗
  const [sourceInfo, setSourceInfo] = useState<{
    visible: boolean;
    sourceInfo: any;
  }>({
    visible: false,
    sourceInfo: {},
  });

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      getData({ current: tableCache.current, size: tableCache.size });
    } else {
      getData({ current: 1 });
    }

    setIsInitialized(true);
  }, []);

  // 获取数据
  const getData = (overlap: CommonObject = {}) => {
    const params = { ...getFilter(), ...overlap }; // review_status: 2 表示二审
    dispatch(getTableList('getRatedList', 'list', params));
  };

  // 显示源稿信息弹窗
  const showSourceInfo = (record: any) => {
    // 这里可以根据实际情况从record中获取源稿信息
    setSourceInfo({
      visible: true,
      sourceInfo: {
        ...record.ugc_article,
      },
    });
  };

  // 获取过滤条件
  const getFilter = () => {
    const { current, size } = tableList;
    const filters: CommonObject = { current, size, review_status: 2 }; // review_status: 2 表示二审

    // 添加所有筛选字段
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== undefined) {
        filters[key] = value;
      }
    });

    return filters;
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    if (key === 'search_type') {
      // 搜索类型变化时，清空关键词和作者建议
      setFilter({
        ...filter,
        [key]: value,
        keyword: '',
      });
      setSearch({
        keyword: '',
        author_id: undefined,
      });
      setAuthorSuggestions([]);
    } else {
      setFilter({
        ...filter,
        [key]: value,
      });
    }
  };

  // 监听 filter 变化
  useEffect(() => {
    if (isInitialized) {
      getData({ current: 1 });
    }
  }, [filter]);

  // 处理搜索
  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      handleSearch();
    }
  };

  // 处理搜索
  const handleSearch = () => {
    if (filter.search_type === 2) {
      // 作者搜索，统一使用 keyword 传递数据
      setFilter({
        ...filter,
        keyword: search.author_id || '',
      });
    } else if (filter.search_type === 3) {
      // ID搜索，使用 keyword
      setFilter({
        ...filter,
        keyword: search.keyword,
      });
    } else {
      setFilter({
        ...filter,
        keyword: search.keyword,
      });
    }
  };

  // 处理作者搜索建议
  const handleAuthorSearch = debounce((value: string) => {
    if (value && filter.search_type === 2) {
      communityApi
        .recommendAccount_Search({ keyword: value })
        .then((res: any) => {
          setAuthorSuggestions(res.data?.list || []);
        })
        .catch(() => {
          setAuthorSuggestions([]);
        });
    } else {
      setAuthorSuggestions([]);
    }
  }, 300);

  // 处理排序
  const handleSort = (sortAsc: number, sortBy: number) => {
    let sort_asc;
    if (sortAsc === 0) {
      sort_asc = 'desc';
    } else if (sortAsc === 1) {
      sort_asc = 'asc';
    } else {
      sort_asc = '';
    }
    setFilter({
      ...filter,
      spread_index: sort_asc,
    });
  };

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUserDetailModal({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  // 获取操作日志
  const getOperateLog = (record: any) => {
    console.log('获取操作日志，记录ID：', record.id);
    dispatch(setConfig({ loading: true }));
    creativeRevenueApi
      .getOperateLog({
        id: record.id,
      })
      .then((r: any) => {
        console.log('操作日志原始数据：', r);
        // 处理操作日志数据，按照图片中显示的数据格式

        dispatch(setConfig({ loading: false }));

        setOperateLog({
          visible: true,
          logs: r.data?.list || [],
        });
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  // 处理修改评级
  const handleModifyRating = (record: any) => {
    setRatingRecord(record);
    setRatingModalVisible(true);
  };

  // 评级成功回调
  const handleRatingSuccess = () => {
    setRatingModalVisible(false);
    setRatingRecord(null);

    // 刷新数据
    getData();
  };

  // 取消评级
  const handleRatingCancel = () => {
    setRatingModalVisible(false);
    setRatingRecord(null);
  };

  // ✅ 处理通过 - 二审需要二次确认
  const handleApprove = (record: any) => {
    Modal.confirm({
      title: '通过后，将向作者发放创作收益',
      content: '',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          await creativeRevenueApi.secondAudit({
            id: record.id,
            review_status: true,
          });
          message.success('审批通过成功');
          getData();
        } catch (error) {
          console.error('审批通过失败：', error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // ✅ 处理不通过 - 二审需要二次确认
  const handleReject = (record: any) => {
    Modal.confirm({
      title: '不通过的稿费数据将回到待评级列表',
      content: '',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          await creativeRevenueApi.secondAudit({
            id: record.id,
            review_status: false,
          });
          message.success('审批成功');
          getData();
        } catch (error) {
          console.error('审批不通过失败：', error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // ✅ 批量通过 - 更新确认文案与图片保持一致
  const handleBatchApprove = () => {
    if (selectedRowKeys.length === 0) {
      message.error('请选择数据');
      return;
    }

    Modal.confirm({
      title: (
        <span>
          已选择<span style={{ color: '#f50' }}>{selectedRowKeys.length}条</span>
          数据，确定批量通过？
        </span>
      ),
      content: '',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          // await creativeRevenueApi.batchSecondApprove({ ids: selectedRowKeys.join(',') });
          await creativeRevenueApi.secondBatchAudit({
            ids: selectedRowKeys.join(','),
            review_status: true,
          });
          message.success(`批量通过成功，共 ${selectedRowKeys.length} 条数据`);
          setSelectedRowKeys([]);
          getData();
        } catch (error) {
          console.error('批量通过失败：', error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // ✅ 批量不通过 - 更新确认文案与图片保持一致
  const handleBatchReject = () => {
    if (selectedRowKeys.length === 0) {
      message.error('请选择数据');
      return;
    }

    Modal.confirm({
      title: (
        <span>
          已选择<span style={{ color: '#f50' }}>{selectedRowKeys.length}条</span>
          数据，确定批量不通过？
        </span>
      ),
      content: '',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          await creativeRevenueApi.secondBatchAudit({
            ids: selectedRowKeys.join(','),
            review_status: false,
          });
          message.success(`批量不通过成功，共 ${selectedRowKeys.length} 条数据`);
          setSelectedRowKeys([]);
          getData();
        } catch (error) {
          console.error('批量不通过失败：', error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // ✅ 处理上榜详情
  const handleFeeDetail = async (record: any) => {
    try {
      const response = await creativeRevenueApi.getRankDetail({
        article_id: record.article_id,
      });

      setRankDetailModal({
        visible: true,
        data: response.data,
        loading: false,
      });
    } catch (error) {
      console.error('获取上榜详情失败：', error);
      setRankDetailModal({
        visible: false,
        data: null,
        loading: false,
      });
    }
  };

  // 处理导出数据
  const handleExport = () => {
    Modal.confirm({
      title: '单次最多可导出2000行数据',
      content: '如果当前列表数量超出上限，仅导出前2000行 ',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        performExport();
      },
    });
  };

  // 执行导出操作
  const performExport = () => {
    // ✅ 创建导出参数，排除分页信息
    const exportParams: CommonObject = { review_status: 2 };

    // 添加所有筛选字段（排除分页参数）
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== undefined) {
        exportParams[key] = value;
      }
    });

    dispatch(setConfig({ loading: true }));
    creativeRevenueApi
      .export(exportParams)
      .then((res: any) => {
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(res.data);
        a.download = `潮新闻-创作收益-稿费评级-待审批(二审)-${moment().format('YYYYMMDD')}.xlsx`;
        a.click();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
        message.error('导出失败');
      });
  };

  // 处理批量选择
  const handleSelectChange = (selectedKeys: any[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 获取序号
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  // 跳转到媒立方
  const toMlf = (id: number, type: 'mlf_detail_url', record?: any, channelID?: string) => {
    releaseListApi
      .toMlf(type, { mlf_id: id })
      .then((r: any) => {
        window.open(r.data.url);
      })
      .catch((error) => {});
  };

  // 获取列配置
  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 60,
    },
    {
      title: (
        <div>
          稿件标题&nbsp;
          <Tooltip title="针对取稿稿费，此处显示媒立方稿件标题" placement="top">
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'list_title',
      key: 'list_title',
      width: 200,
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <a
            onClick={() => {
              if (record.mlf_id) {
                toMlf(record.mlf_id, 'mlf_detail_url');
              } else {
                setPreview({
                  visible: true,
                  data: {
                    article_id: record.article_id,
                    doc_type: 10,
                  },
                  skey: Date.now(),
                });
              }
            }}
          >
            {text}
            {record.mlf_id ? (
              <Button
                type="dashed"
                size="small"
                style={{ padding: 3, paddingTop: 0, paddingBottom: 0, marginLeft: 5 }}
                onClick={(e) => {
                  e.stopPropagation();
                  showSourceInfo(record);
                }}
              >
                源
              </Button>
            ) : (
              ''
            )}
          </a>
        </div>
      ),
    },
    {
      title: (
        <div>
          频道&nbsp;
          <Tooltip title="针对取稿稿费，此处显示媒立方稿件所在频道" placement="top">
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'channel_name',
      key: 'channel_name',
      width: 100,
    },
    {
      title: '等级',
      dataIndex: 'fee_grade_str',
      key: 'fee_grade_str',
      width: 80,
    },
    {
      title: '金额（元）',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      render: (text: number) => {
        if (!text && text !== 0) return '-';
        return Number.isInteger(text) ? text : text.toFixed(2);
      },
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '105px',
          }}
        >
          <SortableColumn
            title="传播指数"
            sort_by={filter.sort_by || 0}
            currentSortBy={1}
            pointerEvents={!filter.keyword}
            onChange={handleSort}
          />
          <Tooltip
            title="稿件发布72小时内的传播情况（包括浏览量及转赞评）"
            placement="top"
            style={{ marginLeft: 10 }}
          >
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'spread_index',
      key: 'spread_index',
      width: 120,
      render: (text: any, record: any) => <div>{text}</div>,
    },
    {
      title: '稿费类型',
      dataIndex: 'fee_type_str',
      key: 'fee_type_str',
      width: 100,
      render: (text: string, record: any) => {
        if (!text) return '-';

        // ✅ 上榜奖励类型添加详情按钮
        if (record.fee_type === 3) {
          return (
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <span>{text}</span>
              <Button
                type="dashed"
                size="small"
                style={{ padding: 3, paddingTop: 0, paddingBottom: 0 }}
                onClick={() => handleFeeDetail(record)}
              >
                详
              </Button>
            </div>
          );
        }

        return text;
      },
    },
    {
      title: '作者',
      dataIndex: 'nick_name_str',
      key: 'nick_name_str',
      width: 120,
      render: (text: string, record: any) => (
        <a onClick={() => showUserDetailModal(record)}>{text}</a>
      ),
    },
    {
      title: '发布时间',
      dataIndex: 'release_time_str',
      key: 'release_time_str',
      width: 160,
      render: (text: string) => text || '-',
    },
    {
      title: '最后操作时间',
      dataIndex: 'update_time_str',
      key: 'update_time_str',
      width: 160,
      render: (text: string, record: any) => (
        <a onClick={() => getOperateLog(record)}>{text || '-'}</a>
      ),
    },
    {
      title: '操作',
      key: 'op',
      width: 180,
      fixed: 'right',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA
            perm="earnings_manuscript_income:update_fee_grade3"
            onClick={() => handleModifyRating(record)}
          >
            修改评级
          </PermA>
          <Divider type="vertical" />

          <PermA
            perm="earnings_manuscript_income:second_review"
            onClick={() => handleApprove(record)}
          >
            通过
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="earnings_manuscript_income:second_review"
            onClick={() => handleReject(record)}
          >
            不通过
          </PermA>
        </span>
      ),
    },
  ];

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="earnings_manuscript_income:export_income" onClick={handleExport}>
            <Icon type="export" /> 导出数据
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item>
                {requirePerm4Function(
                  session,
                  'earnings_manuscript_income:second_review'
                )(
                  <Button
                    onClick={handleBatchApprove}
                    disabled={selectedRowKeys.length === 0}
                    style={{ marginRight: 8 }}
                    loading={loading}
                  >
                    批量通过
                  </Button>
                )}
                {requirePerm4Function(
                  session,
                  'earnings_manuscript_income:second_review'
                )(
                  <Button
                    onClick={handleBatchReject}
                    disabled={selectedRowKeys.length === 0}
                    style={{ marginRight: 8 }}
                    loading={loading}
                  >
                    批量不通过
                  </Button>
                )}
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.fee_type}
                  onChange={(value) => handleFilterChange('fee_type', value)}
                  style={{ width: 120 }}
                  placeholder="稿费类型"
                >
                  <Select.Option value="">稿费类型</Select.Option>
                  <Select.Option value={1}>取稿稿费</Select.Option>
                  <Select.Option value={2}>指定稿费</Select.Option>
                  <Select.Option value={3}>上榜奖励</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.fee_grade}
                  onChange={(value) => handleFilterChange('fee_grade', value)}
                  style={{ width: 120 }}
                  placeholder="稿费等级"
                >
                  <Select.Option value="">稿费等级</Select.Option>
                  <Select.Option value={1}>甲等</Select.Option>
                  <Select.Option value={2}>乙等</Select.Option>
                  <Select.Option value={3}>丙等</Select.Option>
                  <Select.Option value={4}>丁等</Select.Option>
                  <Select.Option value={5}>戊等</Select.Option>
                  <Select.Option value={0}>其他</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.author_type}
                  onChange={(value) => handleFilterChange('author_type', value)}
                  style={{ width: 120 }}
                  placeholder="作者类型"
                >
                  <Select.Option value="">账号类型</Select.Option>
                  <Select.Option value={1}>潮客</Select.Option>
                  <Select.Option value={2}>潮鸣号</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Select
                value={filter.search_type}
                onChange={(value) => handleFilterChange('search_type', value)}
                style={{ width: 100, marginRight: 8 }}
              >
                <Select.Option value={1}>稿件标题</Select.Option>
                <Select.Option value={2}>作者</Select.Option>
                <Select.Option value={3}>潮新闻ID</Select.Option>
              </Select>
              {filter.search_type === 2 ? (
                <Select
                  value={search.author_id}
                  onChange={(value) => {
                    setSearch({ ...search, author_id: value as string });
                  }}
                  onSearch={handleAuthorSearch}
                  placeholder="输入昵称进行搜索"
                  style={{ width: 200, marginRight: 8 }}
                  showSearch
                  allowClear={true}
                  filterOption={false}
                >
                  {authorSuggestions.map((d: any) => (
                    <Select.Option
                      style={{
                        whiteSpace: 'pre-wrap',
                      }}
                      key={d.id}
                      value={d.id}
                    >
                      {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] || ''}${
                        d.nick_name
                      } | 小潮号：${d.chao_id}`}
                    </Select.Option>
                  ))}
                </Select>
              ) : (
                <Input
                  value={search.keyword}
                  onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
                  placeholder={filter.search_type === 3 ? '请输入ID' : '请输入搜索内容'}
                  style={{ width: 160, marginRight: 8 }}
                  onKeyPress={handleKey}
                />
              )}
              <Button type="primary" onClick={handleSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          func="getRatedList"
          index="list"
          rowKey="id"
          filter={getFilter()}
          columns={columns}
          pagination={true}
          multi={true}
          selectedRowKeys={selectedRowKeys}
          onSelectChange={handleSelectChange}
          tableProps={{ scroll: { x: 1600 } }}
        />

        {/* 用户详情弹窗 */}
        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
          onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
        >
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>

        {/* 操作日志弹窗 */}
        <Modal
          visible={operateLog.visible}
          title="操作日志"
          onCancel={() => setOperateLog({ ...operateLog, visible: false })}
          onOk={() => setOperateLog({ ...operateLog, visible: false })}
        >
          <div>
            <Timeline>
              {operateLog.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.actions.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={action.time}
                    key={`time${i}-action${index}`}
                  >
                    {action.user}&emsp;{action.action}
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>

        {/* 修改评级弹窗 */}
        <FeeRatingModal
          visible={ratingModalVisible}
          title="修改评级"
          initialGrade={ratingRecord?.fee_grade}
          record={ratingRecord}
          onCancel={handleRatingCancel}
          onSuccess={handleRatingSuccess}
        />

        {/* 上榜详情弹窗 */}
        <Modal
          visible={rankDetailModal.visible}
          title="上榜详情"
          width={600}
          onCancel={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
          footer={[
            <Button
              key="ok"
              type="primary"
              onClick={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
            >
              确定
            </Button>,
          ]}
          destroyOnClose={true}
        >
          {rankDetailModal.loading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <Icon type="loading" />
            </div>
          ) : (
            <div style={{ padding: '20px 0' }}>
              {rankDetailModal.data?.rank_name ? (
                <div>
                  <a
                    href={rankDetailModal.data?.rank_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ color: '#1890ff', textDecoration: 'none', fontSize: '14px' }}
                  >
                    {rankDetailModal.data.rank_name}
                  </a>
                </div>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                  暂无上榜详情
                </div>
              )}
            </div>
          )}
        </Modal>

        {/* 预览稿件弹窗 */}
        <PreviewMCN
          visible={preview.visible}
          skey={preview.skey}
          data={preview.data}
          onClose={() => setPreview({ ...preview, visible: false })}
        />

        {/* 源稿信息弹窗 */}
        <SourceInfoModal
          visible={sourceInfo.visible}
          sourceInfo={sourceInfo.sourceInfo}
          onCancel={() => setSourceInfo({ ...sourceInfo, visible: false })}
        />
      </div>
    </>
  );
}
