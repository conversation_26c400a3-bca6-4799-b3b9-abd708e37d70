import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCircleSupportRecommend,
  getCrumb,
  jumpToEdit,
  objectToQueryString,
  RecommendTypeNames,
  RecommendTypes,
  recommendTypeText,
  resolveNewsType,
  searchToObject,
  setMenuHook,
  showIDDetailModal,
  UserDetail,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  Button,
  Select,
  Input,
  Menu,
  Dropdown,
  Tag,
  Timeline,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, opApi, releaseListApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton, PermMenuItem } from '@components/permItems';
import Radio from 'antd/es/radio';
import AddArticleModal from './addArticleModal';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import { DOC_TYPE } from '@app/utils/constants';
import EditCircleContentLevelModal from '../community/component/EditCircleContentLevelModal';
import EditCircleInfoModal from '../community/component/EditCircleInfoModal';
import ContentRecommendDrawer from '@app/components/business/recommend/ContentRecommendDrawer';
import CircleBannerDrawer from './circleBannerDrawer';
import _ from 'lodash';
import CircleActiveRankDrawer from './circleActiveRankDrawer';
import CircleActiveDetailDrawer from './circleActiveDetailDrawer';
import CircleActiveUserRewardModal from './circleActiveUserRewardModal';

interface RewardModalState {
  visible: boolean;
  // reward1?: number;
  // reward2?: number;
  // reward3?: number;
  // reward4_10?: number;
  // reward11_20?: number;
}

interface DrawerState {
  visible: boolean;
  record: any | null;
  key: string | null;
}

interface UserDetailState {
  visible: boolean;
  detail: any;
  key: string | null;
}

interface CircleActiveUserMgrProps {
  breadCrumb: string[];
  formContent?: any;
}

export default function CircleActiveUserMgr(props: any) {
  // 获取当前月份
  const month = moment().format('YYYY-MM');
  // 获取2025-06 到现在的所有月份
  const months = [];
  const startDate = moment('2025-06');
  const endDate = moment();

  // 计算上个月的显示格式和数据格式
  const lastMonthDisplay = moment().subtract(1, 'months').format('YYYY-MM');
  const lastMonth = moment().subtract(1, 'months').format('YYYY-MM');

  while (startDate.isSameOrBefore(endDate)) {
    months.push(startDate.format('YYYY-MM'));
    startDate.add(1, 'month');
  }

  // 潮圈ID固定
  const { id: circle_id, name } = useParams<any>();

  const [chao_id, setChaoID] = useState(undefined);
  const [filter, setFilter] = useState<any>({
    circle_id: circle_id,
    state: '',
    month: month,
  });

  const [circleOptions, setCircleOptions] = useState([]);

  const [bannerDrawer, setBannerDrawer] = useState({
    visible: false,
    record: null,
    key: null,
  });

  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const addRef = useRef<any>(null);

  const [accountOptions, setAccountOptions] = useState([]);

  const [preview, setPreview] = useState({
    visible: false,
    skey: Date.now(),
    data: {}
  });

  const [userDetail, setUserDetail] = useState({
    visible: false,
    detail: {},
  });

  const [contentLevel, setContentLevel] = useState({
    visible: false,
    record: {},
  });

  const [circleInfo, setCircleInfo] = useState({
    visible: false,
    record: {},
  });

  const [recDrawer, setRecDrawer] = useState({
    visible: false,
    record: {},
    key: null,
  });

  const [configDrawer, setConfigDrawer] = useState({
    visible: false,
    record: null,
  });

  const [detailDrawer, setDetailDrawer] = useState({
    visible: false,
    record: null,
  });

  const [rewardModal, setRewardModal] = useState({
    visible: false,
    // reward1: undefined as number | undefined,
    // reward2: undefined as number | undefined,
    // reward3: undefined as number | undefined,
    // reward4_10: undefined as number | undefined,
    // reward11_20: undefined as number | undefined
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const updateKeyword = useCallback((e: any) => {
    setChaoID(e.target.value);
  }, []);

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    return x;
  }, [filter]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      const { current } = store.getState().tableList;
      const func = 'getCircleActiveRankList';
      const key = 'list';
      dispatch(
        getTableList(func, key, {
          ...f,
          current,
          size,
          ...overlap,
        })
      );
    },
    [f]
  );

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id,
      name: record.name,
      class_id: record.class_id,
    });
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除"${record.article_title}"？`,
      onOk: () => {
        run(api.deleteRankingArticle, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const listSort = (record: any, i: number, position: number = 1) => {
    let data: any = {
      id: record.id,
      sort_flag: i,
    };
    if (i == 2) {
      data.position = position;
    }
    api.rankingSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.article_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .rankingSort({ id: record.id, sort_flag: 2, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const changeRecOrder = (record: any) => {
    let position = record.seq;
    let sticky = false;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };
    const stickyChange = (e: any) => {
      sticky = e.target.checked;
    };
    Modal.confirm({
      title: <p>排序：《{record.title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={100} defaultValue={position} onChange={positionChange} />
          <span style={{ color: '#ccc' }} key={3}>
            请输入1~100的数字
          </span>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        const MAX_STICKY_POSITION = 100;
        if (!position) {
          message.error('请填写位置');
          return;
        }
        dispatch(setConfig({ loading: true }));
        const data: any = { position, id: record.id };
        opApi
          .moveCircleRecommend(data)
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              getList();
              dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleSort = (record: any, flag: any) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortCircleBanner({ id: record.id, sort_flag: flag })
      .then(() => {
        message.success('操作成功');
        getList();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const handleEditArticle = (record: any) => {
    dispatch(setConfig({ loading: true }));
    releaseListApi
      .getArticleEditUrl({ id: record.id })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        if (res.data.token) {
          jumpToEdit(res.data.token, record.doc_type);
          Modal.confirm({
            title: '是否已经操作完成，需要刷新页面？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              getList();
            },
          });
        }
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const revokeNews = (id: string | number, title: string) => {
    const WAIT_TIME = 1000;
    Modal.confirm({
      title: <p>确认取消签发《{title?.length > 30 ? title.slice(0, 30) + '...' : title}》？</p>,
      content: <p>该作品将变为待审核状态，仅作者本人可见</p>,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeRevokeNews({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              getList();
              dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const changeRecStatus = (record: any, status: number) => {
    dispatch(setConfig({ loading: true }));
    const data: any = { id: record.id, status };
    opApi
      .updateCircleRecommendStatus(data)
      .then(() => {
        message.success('操作成功');
        getList();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const deleteBanner = (record: any) => {
    Modal.confirm({
      title: `确认删除吗？`,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        const data: any = { id: record.id };
        opApi
          .deleteCircleBanner(data)
          .then(() => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const deleteRec = (record: any) => {
    Modal.confirm({
      title: `确认删除推荐位《${record.title}》？`,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        const data: any = { id: record.id };
        opApi
          .deleteCircleRecommend(data)
          .then(() => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleChangeState = (record: any) => {
    let content;
    const isCurrentMonth = month == filter.month;
    if (record.state == 0) {
      // 隐藏
      content = isCurrentMonth
        ? '隐藏后，将不会在前台页面展示，其他账号排名顺延（月末也不会发放奖励）'
        : '隐藏后，将不会在前台页面展示，其他账号排名顺延（已发放的奖励不影响）';
    } else {
      // 取消隐藏
      content = isCurrentMonth
        ? '取消隐藏后，在前台页面正常展示'
        : '取消隐藏后，在前台页面正常展示（不会再发放奖励）';
    }

    Modal.confirm({
      title: content,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        communityApi
          .circleActiveSetHidden({
            circle_id,
            account_id: record.account_id,
            is_hide: record.state == 0 ? 1 : 0,
          })
          .then((r: any) => {
            dispatch(setConfig({ loading: false }));
            message.success('操作成功');
            getList();
          })
          .catch(() => dispatch(setConfig({ loading: false })));
      },
    });
  };

  const [operateLog, setOperateLog] = useState({
    visible: false,
    logs: [],
  });
  const getOperateLog = (record: any) => {
    opApi
      .getOperateLog({ target_id: record.id, type: 166 })
      .then((r: any) => {
        setOperateLog({
          visible: true,
          logs: r.data.admin_log_list,
        });
      })
      .catch();
  };

  const handleUserDetail = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUserDetail({
          visible: true,
          detail: r.data.account,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '用户昵称',
      dataIndex: 'nick_name',
      width: 90,
      render: (text: any, record: any) => <a onClick={() => handleUserDetail(record)}>{text}</a>,
    },
    {
      title: '本月活跃值',
      dataIndex: 'active_scores',
      width: 90,
      render: (text: any, record: any) => <a onClick={() => showScoreDetail(record)}>{text}</a>,
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 90,
      render: (text: any, record: any) => (text == 0 ? '正常' : '隐藏'),
    },

    {
      title: '操作',
      key: 'op',
      width: 100,
      fixed: 'right',
      render: (text: any, record: any) => (
        <PermA perm="" onClick={() => handleChangeState(record)}>
          {record.state == 0 ? '隐藏' : '取消隐藏'}
        </PermA>
      ),
    },
  ];

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, {
      selectKeys: [`/view/circleActive/${circle_id}`],
      openKeys: props.breadCrumb,
    });
    getCircleList();
  }, []);

  useEffect(() => {
    getList({ current: 1 });
  }, [f]);

  const getCircleList = () => {
    communityApi
      .getCircleList({ current: 1, enabled: 'true', size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any;
        setCircleOptions(list.records);
      })
      .catch(() => {});
  };

  const formChange = (v: any) => {
    if (typeof v === 'object') {
      setForm({
        ...form,
        name: v.target.value,
      });
    } else {
      setForm({
        ...form,
        class_id: v,
      });
    }
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const addRecord = () => {
    addRef.current.showModal();
  };

  const handleAddArticleOk = () => {
    getList({ current: 1 });
  };

  const publish = () => {
    if (current != 1) {
      message.error('请翻到第一页，发布前20条稿件');
      return;
    }

    if (records.length < 20) {
      message.error('稿件少于20条，无法发布');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const handleKey = (e: any) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        chao_id: chao_id || '',
      });
    }
  };

  const config = () => {
    run(communityApi.circleActiveConfigDetail, { circle_id }, true)
      .then((r: any) => {
        setConfigDrawer({
          visible: true,
          record: r.data?.circleActiveConfig,
        });
      })
      .catch(() => {});
  };

  const showScoreDetail = (record: any) => {
    setDetailDrawer({
      visible: true,
      record: record,
    });
  };

  // const handleReward = () => {
  //   if (!rewardModal.reward1 || !rewardModal.reward2 || !rewardModal.reward3 || !rewardModal.reward4_10 || !rewardModal.reward11_20) {
  //     message.error('请填写完整的奖金设置');
  //     return;
  //   }

  //   const circle_active_rank_money_setting = {
  //     rank_1: rewardModal.reward1,
  //     rank_2: rewardModal.reward2,
  //     rank_3: rewardModal.reward3,
  //     rank_4_10: rewardModal.reward4_10,
  //     rank_11_20: rewardModal.reward11_20
  //   };

  //   run(communityApi.giveCircleActiveReward, {
  //     source: 3,
  //     rank_month: lastMonth,
  //     circle_active_rank_money_setting
  //   }, true).then(() => {
  //     message.success('发放奖励成功');
  //     setRewardModal({
  //       visible: false,
  //       reward1: undefined,
  //       reward2: undefined,
  //       reward3: undefined,
  //       reward4_10: undefined,
  //       reward11_20: undefined
  //     });
  //   });
  // };

  // const resetRewardModal = () => {
  //   setRewardModal({
  //     visible: false,
  //     reward1: undefined,
  //     reward2: undefined,
  //     reward3: undefined,
  //     reward4_10: undefined,
  //     reward11_20: undefined
  //   });
  // };
  
  const handleReward = () => {
    communityApi.circleActiveConfigDetail({ circle_id }).then((r: any) => {
        if (r.data?.circleActiveConfig && r.data?.circleActiveConfig.sign_in_enable) {
          setRewardModal({ visible: true })
        }
        else {
          message.error('当前圈子未开启活跃圈友功能')
        }
    })
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>
            返回
          </Button>

          <PermButton perm="" onClick={() => config()}>
            功能配置
          </PermButton>
          <PermButton 
            perm="" 
            style={{ marginLeft: 8 }} 
            onClick={() => handleReward()}
          >
            发放奖励
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, name])}
        </Col>
      </Row>
      <div className="component-content community_recommend_list">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Select
              value={filter.month}
              style={{ width: 110, marginRight: 8 }}
              onChange={(month: any) => setFilter({ ...filter, month })}
            >
              {months.map((month: any) => (
                <Select.Option key={`month_${month}`} value={month}>
                  {month}
                </Select.Option>
              ))}
            </Select>
            <Select
              value={filter.state}
              style={{ width: 110, marginRight: 8 }}
              onChange={(state: any) => setFilter({ ...filter, state })}
            >
              <Select.Option value="">筛选状态</Select.Option>
              <Select.Option value={0}>正常</Select.Option>
              <Select.Option value={1}>隐藏</Select.Option>
            </Select>

            <span>注：本月榜单每小时更新统计（前台显示前100名）；往月榜单生成后不再变动（前台显示前20名）<a href="https://app-stc.zjol.com.cn/assets/********/1756283962534_68aec43aa7325f00014bed76.png" target="_blank">查看详细计算规则</a></span>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Select
              style={{ width: 200, marginRight: 8 }}
              placeholder="请输入昵称或小潮号搜索"
              onSearch={handleAccountSearch}
              showSearch
              allowClear
              disabled={!!props.formContent}
              filterOption={false}
              value={chao_id}
              onChange={(val: any) => {
                setChaoID(val);
              }}
            >
              {accountOptions.map((item: any, index: number) => (
                <Select.Option
                  style={{
                    whiteSpace: 'pre-wrap',
                  }}
                  key={`${item.chao_id}_${index}`}
                  value={item.chao_id}
                >
                  {item.nick_name}|用户ID：{item.chao_id}
                </Select.Option>
              ))}
            </Select>
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func={'getCircleActiveRankList'}
          index={'list'}
          filter={f}
          columns={columns}
          rowKey="id"
          pagination={true}
        />
        <AddArticleModal
          wrappedComponentRef={addRef}
          maxPosition={total + 1}
          type={filter.type}
          onOk={handleAddArticleOk}
        ></AddArticleModal>
        <PreviewMCN 
          visible={preview.visible} 
          skey={preview.skey} 
          data={preview.data} 
          onClose={() => setPreview({ visible: false, skey: Date.now(), data: {} })} 
        />
        <EditCircleContentLevelModal
          idKey="id"
          visible={contentLevel.visible}
          record={contentLevel.record}
          onCancel={() => setContentLevel({ visible: false, record: {} })}
          onEnd={() => {
            getList();
            setContentLevel({ visible: false, record: {} });
          }}
        />
        <EditCircleInfoModal
          idKey="id"
          visible={circleInfo.visible}
          record={circleInfo.record}
          onCancel={() => setCircleInfo({ visible: false, record: {} })}
          onEnd={() => {
            getList();
            setCircleInfo({ visible: false, record: {} });
          }}
        />
        <Modal
          visible={userDetail.visible}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetail({ visible: false, detail: {} })}
          onOk={() => setUserDetail({ visible: false, detail: {} })}
        >
          {userDetail.visible && <UserDetail detail={userDetail.detail} />}
        </Modal>

        <CircleActiveRankDrawer
          visible={configDrawer.visible}
          record={configDrawer.record}
          circle_id={circle_id}
          onClose={() => setConfigDrawer({ visible: false, record: null })}
          onEnd={() => {
            getList();
            setConfigDrawer({ visible: false, record: null });
          }}
        />
        <CircleActiveDetailDrawer
          visible={detailDrawer.visible}
          record={detailDrawer.record}
          circle_id={circle_id}
          circle_name={name}
          month={filter.month}
          onClose={() => setDetailDrawer({ visible: false, record: null })}
          onEnd={() => {
            getList();
            setDetailDrawer({ visible: false, record: null });
          }}
        />

        <CircleActiveUserRewardModal
          visible={rewardModal.visible}
          circle_id={circle_id}
          circle_name={name}
          month={moment().subtract(1, 'months').format('YYYY-MM')}
          onClose={() => { setRewardModal({ visible: false }) }}
          onEnd={() => {
            setRewardModal({ visible: false })
          }}
        />
        {/* <Modal
          // title="发放奖励"
          visible={rewardModal.visible}
          width={500}
          onCancel={resetRewardModal}
          onOk={handleReward}
        >
          <Row>
            <Col span={6}>
              <h3>发放奖励</h3>
            </Col>
            <Col span={4} style={{ lineHeight: '30px' }}>
            榜单周期：
            </Col>
            <Col span={14} style={{ lineHeight: '30px', fontWeight: 'bold' }}>
              {lastMonthDisplay}
            </Col>
            <Col span={24} style={{ marginTop: '20px', marginBottom: '10px', fontWeight: 'bold' }}>
              <span style={{ color: '#ff4d4f' }}>*</span>金额设置
            </Col>
            <Col span={24} style={{ marginBottom: '10px' }}>
              <Row align="middle">
                <Col span={6} style={{ lineHeight: '32px' }}>第1名：</Col>
                <Col span={12}>
                  <InputNumber
                    style={{ width: '100px' }}
                    min={1}
                    max={500}
                    value={rewardModal.reward1}
                    onChange={(value) => setRewardModal({ ...rewardModal, reward1: value })}
                  />
                  <span style={{ marginLeft: '8px' }}>元（不超过500）</span>
                </Col>
              </Row>
            </Col>
            <Col span={24} style={{ marginBottom: '10px' }}>
              <Row align="middle">
                <Col span={6} style={{ lineHeight: '32px' }}>第2名：</Col>
                <Col span={12}>
                  <InputNumber
                    style={{ width: '100px' }}
                    min={1}
                    max={500}
                    value={rewardModal.reward2}
                    onChange={(value) => setRewardModal({ ...rewardModal, reward2: value })}
                  />
                  <span style={{ marginLeft: '8px' }}>元（不超过500）</span>
                </Col>
              </Row>
            </Col>
            <Col span={24} style={{ marginBottom: '10px' }}>
              <Row align="middle">
                <Col span={6} style={{ lineHeight: '32px' }}>第3名：</Col>
                <Col span={12}>
                  <InputNumber
                    style={{ width: '100px' }}
                    min={1}
                    max={500}
                    value={rewardModal.reward3}
                    onChange={(value) => setRewardModal({ ...rewardModal, reward3: value })}
                  />
                  <span style={{ marginLeft: '8px' }}>元（不超过500）</span>
                </Col>
              </Row>
            </Col>
            <Col span={24} style={{ marginBottom: '10px' }}>
              <Row align="middle">
                <Col span={6} style={{ lineHeight: '32px' }}>第4-10名：</Col>
                <Col span={12}>
                  <InputNumber
                    style={{ width: '100px' }}
                    min={1}
                    max={300}
                    value={rewardModal.reward4_10}
                    onChange={(value) => setRewardModal({ ...rewardModal, reward4_10: value })}
                  />
                  <span style={{ marginLeft: '8px' }}>元（不超过300）</span>
                </Col>
              </Row>
            </Col>
            <Col span={24} style={{ marginBottom: '10px' }}>
              <Row align="middle">
                <Col span={6} style={{ lineHeight: '32px' }}>第11-20名：</Col>
                <Col span={12}>
                  <InputNumber
                    style={{ width: '100px' }}
                    min={1}
                    max={100}
                    value={rewardModal.reward11_20}
                    onChange={(value) => setRewardModal({ ...rewardModal, reward11_20: value })}
                  />
                  <span style={{ marginLeft: '8px' }}>元（不超过100）</span>
                </Col>
              </Row>
            </Col>
            <Col span={24} style={{ marginTop: '10px', color: '#666' }}>
              系统将为上榜账号自动生成相应奖励的稿费数据，显示在稿费待评级列表，经由领导审批后生效。已发放的周期，不会重复发放。
            </Col>
          </Row>
        </Modal> */}
      </div>
    </>
  );
}
