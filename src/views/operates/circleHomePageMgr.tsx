import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCircleSupportRecommend,
  getCrumb,
  jumpToEdit,
  objectToQueryString,
  RecommendTypeNames,
  RecommendTypes,
  recommendTypeText,
  resolveNewsType,
  searchToObject,
  setMenuHook,
  showIDDetailModal,
  UserDetail,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  Button,
  Select,
  Input,
  Menu,
  Dropdown,
  Tag,
  Timeline,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, opApi, releaseListApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton, PermMenuItem } from '@components/permItems';
import Radio from 'antd/es/radio';
import AddArticleModal from './addArticleModal';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import { DOC_TYPE } from '@app/utils/constants';
import EditCircleContentLevelModal from '../community/component/EditCircleContentLevelModal';
import EditCircleInfoModal from '../community/component/EditCircleInfoModal';
import ContentRecommendDrawer from '@app/components/business/recommend/ContentRecommendDrawer';
import CircleBannerDrawer from './circleBannerDrawer';
import CircleTopConfigModal from '../community/component/circleTopConfigModal';

export default function CircleHomePageMgr(props: any) {
  // 潮圈ID固定
  const channelId = searchToObject().channel_id;

  const [listSelectedKeys, setListSelectedKeys] = useState([]);
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: '',
  });
  const [filter, setFilter] = useState<any>({
    type: parseInt(searchToObject().type ?? 0),
    status: parseInt(searchToObject().status ?? 1),
    channel_id: channelId,
    circle_id: '',
  });

  const [circleOptions, setCircleOptions] = useState([]);

  const [bannerDrawer, setBannerDrawer] = useState({
    visible: false,
    record: null,
    key: null,
  });

  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const [keyword, setKeyword] = useState('');
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const addRef = useRef<any>(null);

  const [preview, setPreview] = useState({
    visible: false,
    skey: 0,
    data: {},
  });

  const [userDetail, setUserDetail] = useState({
    visible: false,
    detail: {},
    key: null,
  });

  const [contentLevel, setContentLevel] = useState({
    visible: false,
    record: {},
    key: null,
  });

  const [circleInfo, setCircleInfo] = useState({
    visible: false,
    record: {},
    key: null,
  });

  const [recDrawer, setRecDrawer] = useState({
    visible: false,
    record: {},
    key: null,
  });

  const [topConfig, setTopConfig] = useState({
    visible: false,
    record: null,
    key: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const updateKeyword = useCallback((e: any) => {
    setKeyword(e.target.value);
  }, []);

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      setListSelectedKeys([]);
      const { current } = store.getState().tableList;
      const func =
        f.type == 0
          ? 'getCircleBannerList'
          : f.type == 1
          ? 'getCircleHomeRecommendList'
          : 'getCircleHotList';
      const key = f.type == 0 ? 'list' : f.type == 1 ? 'recommends' : 'release_list';
      dispatch(
        getTableList(func, key, {
          ...f,
          current,
          size,
          ...overlap,
        })
      );
    },
    [f, setListSelectedKeys]
  );

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id,
      name: record.name,
      class_id: record.class_id,
    });
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除“${record.article_title}”？`,
      onOk: () => {
        run(api.deleteRankingArticle, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const listSort = (record: any, i: number, position: number = 1) => {
    let data: any = {
      id: record.id,
      sort_flag: i,
    };
    if (i == 2) {
      data.position = position;
    }
    api.rankingSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.article_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .rankingSort({ id: record.id, sort_flag: 2, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const changeRecOrder = (record: any) => {
    let position = record.seq;
    let sticky = false;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };
    const stickyChange = (e: any) => {
      sticky = e.target.checked;
    };
    Modal.confirm({
      title: <p>排序：《{record.title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={100} defaultValue={position} onChange={positionChange} />
          <span style={{ color: '#ccc' }} key={3}>
            请输入1~100的数字
          </span>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        const MAX_STICKY_POSITION = 100;
        if (!position) {
          message.error('请填写位置');
          return;
        }
        dispatch(setConfig({ loading: true }));
        const data: any = { position, id: record.id };
        opApi
          .moveCircleRecommend(data)
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              getList();
              dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleSort = (record: any, flag: any) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortCircleBanner({ id: record.id, sort_flag: flag })
      .then(() => {
        message.success('操作成功');
        getList();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const handleEditArticle = (record: any) => {
    dispatch(setConfig({ loading: true }));
    releaseListApi
      .getArticleEditUrl({ id: record.id })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        if (res.data.token) {
          jumpToEdit(res.data.token, record.doc_type);
          Modal.confirm({
            title: '是否已经操作完成，需要刷新页面？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              getList();
            },
          });
        }
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const revokeNews = (id: string | number, title: string) => {
    const WAIT_TIME = 1000;
    Modal.confirm({
      title: <p>确认取消签发《{title?.length > 30 ? title.slice(0, 30) + '...' : title}》？</p>,
      content: <p>该作品将变为待审核状态，仅作者本人可见</p>,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeRevokeNews({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              getList();
              dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleEditContentLevel = (record: any) => {
    setContentLevel({
      visible: true,
      record,
      key: Date.now(),
    });
  };

  const handleEditCircleInfo = (record: any) => {
    setCircleInfo({
      visible: true,
      record,
      key: Date.now(),
    });
  };

  const handleTopConfig = () => {
    run(communityApi.getCircleTopConfigDetail, {}, true).then((res: any) => {
      setTopConfig({
        visible: true,
        record: res.data,
        key: Date.now(),
      });
    });
  };

  const handleCreateRecommend = () => {
    setRecDrawer({
      visible: true,
      record: null,
      key: Date.now(),
    });
  };

  const handleEditRecommend = (record: any = null) => {
    setRecDrawer({
      visible: true,
      record: record,
      key: Date.now(),
    });
  };

  const changeBannerStatus = (record: any, status: number) => {
    dispatch(setConfig({ loading: true }));
    const data: any = { id: record.id, status };
    opApi
      .updateCircleBannerStatus(data)
      .then(() => {
        message.success('操作成功');
        getList();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const changeRecStatus = (record: any, status: number) => {
    dispatch(setConfig({ loading: true }));
    const data: any = { id: record.id, status };
    opApi
      .updateCircleRecommendStatus(data)
      .then(() => {
        message.success('操作成功');
        getList();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const deleteBanner = (record: any) => {
    Modal.confirm({
      title: `确认删除吗？`,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        const data: any = { id: record.id };
        opApi
          .deleteCircleBanner(data)
          .then(() => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const deleteRec = (record: any) => {
    Modal.confirm({
      title: `确认删除推荐位《${record.title}》？`,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        const data: any = { id: record.id };
        opApi
          .deleteCircleRecommend(data)
          .then(() => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const getBannerDropDown = (record: any) => {
    const menu = (
      <Menu>
        <PermMenuItem perm="circle_carousel:58:update" onClick={() => addBanner(record)}>
          编辑
        </PermMenuItem>
        {record.status == 1 ? (
          <PermMenuItem
            perm="circle_carousel:58:update_status"
            onClick={() => changeBannerStatus(record, 0)}
          >
            下架
          </PermMenuItem>
        ) : (
          <PermMenuItem
            perm="circle_carousel:58:update_status"
            onClick={() => changeBannerStatus(record, 1)}
          >
            上架
          </PermMenuItem>
        )}
        <PermMenuItem perm="circle_carousel:58:delete" onClick={() => deleteBanner(record)}>
          删除
        </PermMenuItem>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };

  const getRecDropDown = (record: any) => {
    const menu = (
      <Menu>
        <PermMenuItem
          perm={`content_recommend:${channelId}:update`}
          onClick={() => handleEditRecommend(record)}
        >
          编辑
        </PermMenuItem>
        <PermMenuItem
          perm={`content_recommend:${channelId}:update_sort`}
          onClick={() => changeRecOrder(record)}
          disabled={record.status == 0}
        >
          排序
        </PermMenuItem>
        <PermMenuItem
          perm={`content_recommend:${channelId}:update_status`}
          onClick={() => changeRecStatus(record, record.status == 1 ? 0 : 1)}
        >
          {record.status == 1 ? '下架' : '上架'}
        </PermMenuItem>
        <PermMenuItem
          perm={`content_recommend:${channelId}:delete`}
          onClick={() => deleteRec(record)}
        >
          删除
        </PermMenuItem>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };

  const getHotDropDown = (record: any) => {
    const menu = (
      <Menu>
        <PermMenuItem
          perm="circle_hot:article_edit"
          disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
          onClick={() => handleEditArticle(record)}
        >
          编辑作品
        </PermMenuItem>
        <PermMenuItem perm={`circle_hot:level_edit`} onClick={() => handleEditContentLevel(record)}>
          修改内容等级
        </PermMenuItem>
        <PermMenuItem perm={`circle_hot:circle_edit`} onClick={() => handleEditCircleInfo(record)}>
          修改圈子信息
        </PermMenuItem>
        <PermMenuItem
          perm={`circle_hot:article_cancel`}
          onClick={() => revokeNews(record.id, record.list_title)}
        >
          取消签发
        </PermMenuItem>
        <Menu.Item disabled={record.url === ''}>
          <a href={record.url} target="_blank">
            查看链接
          </a>
        </Menu.Item>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };

  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUserDetail({
          key: Date.now(),
          visible: true,
          detail: r.data.account,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  const [operateLog, setOperateLog] = useState({
    visible: false,
    logs: [],
  });
  const getOperateLog = (record: any) => {
    opApi
      .getOperateLog({ target_id: record.id, type: 166 })
      .then((r: any) => {
        setOperateLog({
          visible: true,
          logs: r.data.admin_log_list,
        });
      })
      .catch();
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="circle_carousel:58:update_sort"
          pos={i}
          start={0}
          end={allData.on_show_count - 1}
          disable={record.status == 0}
          onUp={() => handleSort(record, 0)}
          onDown={() => handleSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '名称',
      dataIndex: 'title',
      width: 90,
    },
    {
      title: '封面图',
      key: 'pic_url',
      dataIndex: 'pic_url',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (
        <div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
        </div>
      ),
    },
    {
      title: '链接',
      dataIndex: 'url',
      width: 90,
      render: (text: any, record: any) => (
        <a href={text} target="_blank" rel="noopener noreferrer">
          {text}
        </a>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 90,
      render: (text: any, record: any) => (text == 1 ? '展示中' : '未展示'),
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 90,
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>
            <div>{moment(text).format('YYYY-MM-DD')}</div>
            <div>{moment(text).format('HH:mm:ss')}</div>
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'op',
      width: 70,
      fixed: 'right',
      render: (text: any, record: any) => getBannerDropDown(record),
    },
  ];

  const recColumns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '潮新闻ID',
      dataIndex: 'id',
      width: 90,
    },
    {
      title: '推荐位标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: any, record: any) => (
        <>
          <PermA
            perm={`content_recommend:${channelId}:update`}
            onClick={() => handleEditRecommend(record)}
            className={`list-title ${record.status == 1 ? '' : 'hide-title'} ${
              record.fixed_number > 0 ? 'fixed-title' : ''
            }`}
          >
            {text || '-'}
            {record.status == 1 ? '' : '（隐藏）'}
          </PermA>
        </>
      ),
    },
    {
      title: '推荐位类型',
      dataIndex: 'ref_type',
      key: 'ref_type',
      render(ref_type: number) {
        return RecommendTypeNames[ref_type];
      },
      width: 90,
    },
    {
      title: (
        <span>
          列表排序&nbsp;
          <Tooltip
            title="固定显示在潮圈首页-按热门排序列表的指定位置，与圈子内容混排"
            overlayStyle={{ maxWidth: 426 }}
          >
            <Icon type="question-circle-o" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'position',
      width: 90,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 70,
      render: (text: any, record: any) => (text == 1 ? '展示中' : '未展示'),
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 150,
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      width: 160,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
        );
      },
    },
    {
      title: '操作',
      key: 'op',
      width: 70,
      fixed: 'right',
      render: (text: any, record: any) => getRecDropDown(record),
    },
  ];

  const hotColumns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '潮新闻ID',
      dataIndex: 'id',
      width: 90,
      render: (text: any, record: any) => <a onClick={() => showIDDetailModal(record)}>{text}</a>,
    },
    {
      title: '标题',
      key: 'list_title',
      dataIndex: 'list_title',
      render: (text: any, record: any) => (
        <span>
          <a
            onClick={() =>
              setPreview({
                visible: true,
                skey: Date.now(),
                data: record,
              })
            }
            className={`list-title ${record.visible ? '' : 'hide-title'} ${
              record.fixed_number > 0 ? 'fixed-title' : ''
            }`}
          >
            {(text?.length > 30 ? text.slice(0, 30) + '...' : text) || '-'}
            {/* {record.visible ? '' : '（隐藏）'}
            {record.fixed_number > 0 ? '（固定）' : ''} */}
          </a>
        </span>
      ),
    },
    {
      title: '封面图',
      key: 'list_pics',
      dataIndex: 'list_pics',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (
        <div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text} imgs={record.pic_array}></ImagePreviewColumn>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'doc_type',
      width: 70,
      render(doc_type: number) {
        return resolveNewsType(doc_type, 1);
      },
    },
    {
      title: '圈子',
      key: 'circle_name',
      dataIndex: 'circle_name',
      width: 90,
      render: (circle_name: string, record: any) =>
        circle_name ? `${circle_name}${record.circle_enable ? '' : '(已下线)'}` : '',
    },
    {
      title: '作者',
      key: 'account_nick_name',
      dataIndex: 'account_nick_name',
      width: 110,
      render: (text: any, record: any) => (
        <a onClick={() => showUserDetailModal(record, true)}>{text}</a>
      ),
    },
    {
      title: '发布时间',
      dataIndex: 'published_at',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <a style={{ whiteSpace: 'pre-wrap' }} onClick={() => getOperateLog(record)}>
            {moment(text).format('YYYY-MM-DD\nHH:mm:ss')}
          </a>
        );
      },
    },
    {
      title: '操作',
      key: 'op',
      width: 70,
      fixed: 'right',
      render: (text: any, record: any) => getHotDropDown(record),
    },
  ];

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, {
      selectKeys: ['/view/circleCommunity?channel_id=' + channelId],
      openKeys: props.openKeys,
    });
    getCircleList();
  }, []);

  useEffect(() => {
    getList({ current: 1 });
  }, [f]);

  useEffect(() => {
    setListSelectedKeys([]);
  }, [current]);

  const getCircleList = () => {
    communityApi
      .getCircleList({ current: 1, enabled: 'true', size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any;
        setCircleOptions(list.records);
      })
      .catch(() => {});
  };

  const handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    setListSelectedKeys(selectedRowKeys);
  };

  const batchDelete = () => {
    run(api.deleteRankingArticle, { id: listSelectedKeys.join(',') }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const formChange = (v: any) => {
    if (typeof v === 'object') {
      setForm({
        ...form,
        name: v.target.value,
      });
    } else {
      setForm({
        ...form,
        class_id: v,
      });
    }
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const addRecord = () => {
    addRef.current.showModal();
  };

  const handleAddArticleOk = () => {
    getList({ current: 1 });
  };

  const publish = () => {
    if (current != 1) {
      message.error('请翻到第一页，发布前20条稿件');
      return;
    }

    if (records.length < 20) {
      message.error('稿件少于20条，无法发布');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const handleKey = (e: any) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        search_type: searchState.search_type,
        keyword: searchState.keyword,
      });
    }
  };

  const addBanner = (record = null) => {
    setBannerDrawer({
      visible: true,
      record,
      key: Date.now(),
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={filter.type}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'type')}
            style={{ marginRight: 8 }}
          >
            <Radio.Button value={0}>轮播图</Radio.Button>
            <Radio.Button value={1}>推荐位</Radio.Button>
            <Radio.Button value={2}>热门内容</Radio.Button>
          </Radio.Group>

          {filter.type == 2 && (
            <Tooltip
              title={
                <>
                  1、热门内容定义——在已关联圈子且非全局沉底的前提下，满足以下任一条件：1）推荐等级；2）圈子精选；3）除作者本人外的赞评数≥5；系统每隔1小时更新一次热门内容数据；
                  <br />
                  2、如需移除热门内容，可设为全局沉底、改为无圈子或取消签发；
                  <br />
                  3、如需添加热门内容，在已关联圈子且非全局沉底的前提下，设为推荐或圈子精选即可
                </>
              }
            >
              <Icon type="question-circle-o" />
            </Tooltip>
          )}

          {filter.type == 0 && (
            <PermButton perm="circle_carousel:58:create" onClick={() => addBanner()}>
              添加轮播图
            </PermButton>
          )}
          {filter.type == 1 && (
            <PermButton
              perm={`content_recommend:${channelId}:create`}
              onClick={() => handleCreateRecommend()}
            >
              创建推荐位
            </PermButton>
          )}

          <PermButton
            perm={``}
            style={{
              marginLeft: '8px',
            }}
            onClick={() => handleTopConfig()}
          >
            双列置顶内容
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content community_recommend_list">
        {filter.type == 2 && (
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Select
                style={{ width: 160 }}
                value={filter.circle_id}
                onChange={(v: any) => setFilter({ ...filter, circle_id: v })}
              >
                <Select.Option value="">按圈子筛选</Select.Option>
                <Select.Option value="0">无圈子</Select.Option>
                {circleOptions.map((item: any) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={searchState.search_type}
                style={{ width: 110, marginRight: 8 }}
                onChange={(search_type: any) => setSearchState({ ...searchState, search_type })}
              >
                <Select.Option value={1}>标题</Select.Option>
                <Select.Option value={2}>作者</Select.Option>
                <Select.Option value={3}>潮新闻ID</Select.Option>
              </Select>
              <Input
                value={searchState.keyword}
                style={{ marginRight: 8, width: 160 }}
                onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
                onKeyPress={handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={() => handleKey({ which: 13 })}>
                <Icon type="search" /> 搜索
              </Button>
            </Col>
          </Row>
        )}
        <Table
          func={
            f.type == 0
              ? 'getCircleBannerList'
              : f.type == 1
              ? 'getCircleHomeRecommendList'
              : 'getCircleHotList'
          }
          index={f.type == 0 ? 'list' : f.type == 1 ? 'recommends' : 'release_list'}
          filter={f}
          columns={f.type == 0 ? columns : f.type == 1 ? recColumns : hotColumns}
          rowKey="id"
          pagination={true}
        />
        <AddArticleModal
          wrappedComponentRef={addRef}
          maxPosition={total + 1}
          type={filter.type}
          onOk={handleAddArticleOk}
        ></AddArticleModal>
        <PreviewMCN {...preview} onClose={() => setPreview({ ...preview, visible: false })} />

        <EditCircleContentLevelModal
          idKey="id"
          {...contentLevel}
          onCancel={() => setContentLevel({ ...contentLevel, visible: false })}
          onEnd={() => {
            getList();
            setContentLevel({ ...contentLevel, visible: false });
          }}
        />
        <EditCircleInfoModal
          idKey="id"
          {...circleInfo}
          onCancel={() => setCircleInfo({ ...circleInfo, visible: false })}
          onEnd={() => {
            getList();
            setCircleInfo({ ...circleInfo, visible: false });
          }}
        />

        <Modal
          visible={userDetail.visible}
          key={userDetail.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetail({ ...userDetail, visible: false })}
          onOk={() => setUserDetail({ ...userDetail, visible: false })}
        >
          {userDetail.visible && <UserDetail detail={userDetail.detail} />}
        </Modal>

        <ContentRecommendDrawer
          skey={recDrawer.key}
          // maxPosition={searchCondition ? 999999999999 : this.state.total + this.state.fixedCount}
          record={recDrawer.record}
          visible={recDrawer.visible}
          supportTypes={getCircleSupportRecommend()}
          onEnd={() => {
            getList();
            setRecDrawer({ ...recDrawer, visible: false });
          }}
          onClose={() => {
            setRecDrawer({ ...recDrawer, visible: false });
          }}
          article_category={2}
        />
        <CircleBannerDrawer
          {...bannerDrawer}
          onClose={() => setBannerDrawer({ ...bannerDrawer, visible: false })}
          onEnd={() => {
            getList();
            setBannerDrawer({ ...bannerDrawer, visible: false });
          }}
        />

        <CircleTopConfigModal
          {...topConfig}
          onCancel={() => setTopConfig({ ...topConfig, visible: false })}
          onOk={() => {
            setTopConfig({ ...topConfig, visible: false });
          }}
        ></CircleTopConfigModal>

        {/* 操作日志 */}
        <Modal
          visible={operateLog.visible}
          title="操作日志"
          cancelText={null}
          onCancel={() =>
            setOperateLog({
              ...operateLog,
              visible: false,
            })
          }
          onOk={() =>
            setOperateLog({
              ...operateLog,
              visible: false,
            })
          }
        >
          <div>
            <Timeline>
              {operateLog.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.log_list?.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={moment(action.created_at).format('HH:mm:ss')}
                    key={`time${i}-action${index}`}
                  >
                    {action.admin_name}&emsp;{action.remark}
                    {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
