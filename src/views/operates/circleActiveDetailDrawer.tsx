import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  Switch,
  Table,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { communityApi, opApi } from '@app/api';
import '@components/business/styles/business.scss';
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import { PermButton } from '@app/components/permItems';
import moment from 'moment';
import { initial } from 'lodash';
import ReactWEditor from 'wangeditor-for-react/lib/core';

const CircleActiveDetailDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const descElRef = React.createRef<ReactWEditor>();

  const { title, pic_url, url } = props.record || {};

  const { getFieldDecorator, getFieldValue } = props.form;
  const [dataSource, setDataSource] = useState<any>([]);
  // circle/active/score_detail

  useEffect(() => {
    if (props.visible) {
      getScoreDetail();
    }
  }, [props.visible]);

  const getScoreDetail = () => {
    dispatch(setConfig({ mLoading: true }));
    communityApi
      .circleActiveScoreDetail({
        circle_id: props.circle_id,
        month: props.month,
        chao_id: props.record?.chao_id,
      })
      .then((r: any) => {
        dispatch(setConfig({ mLoading: false }));
        const data = r.data?.user_circle_active_total_score;
        setDataSource([
          {
            key: '1',
            category: '发布情况',
            dimension: '发稿数',
            score: data.publish_article_scores ?? 0,
          },
          {
            key: '2',
            category: '',
            dimension: '发出评论数',
            score: data.send_comment_scores ?? 0,
          },
          {
            key: '3',
            category: '',
            dimension: '发出点赞数',
            score: data.send_like_scores ?? 0,
          },
          {
            key: '4',
            category: '作品人气/质量',
            dimension: '发稿取签/精选数',
            score: data.publish_featured_scores ?? 0,
          },
          {
            key: '5',
            category: '',
            dimension: '新增阅读数多的作品数',
            score: data.read_scores ?? 0,
          },
          {
            key: '6',
            category: '',
            dimension: '作品新增评论数',
            score: data.receive_comment_scores ?? 0,
          },
          {
            key: '7',
            category: '',
            dimension: '作品新增点赞数',
            score: data.receive_like_scores ?? 0,
          },
          {
            key: '8',
            category: '打卡天数',
            dimension: '圈内打卡天数',
            score: data.clock_scores ?? 0,
          },
          {
            key: '9',
            category: '合计',
            dimension: '',
            score: data.active_scores ?? 0,
          },
        ]);
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  // 表格列
  const columns = [
    {
      title: '大类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '维度',
      dataIndex: 'dimension',
      key: 'dimension',
    },
    {
      title: '计分（超过设定上限的分数最终不计入，本列显示数已按上限做处理）',
      dataIndex: 'score',
      key: 'score',
    },
  ];

  return (
    <Drawer
      title={
        <>
          {props.record?.nick_name}-{props.circle_name}-活跃值明细-{props.month}
          <a
            href="https://app-stc.zjol.com.cn/assets/20250827/1756283962534_68aec43aa7325f00014bed76.png"
            target="_blank"
            style={{ marginLeft: 10 }}
          >
            计分规则
          </a>
        </>
      }
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      footer={<></>}
      maskClosable={true}
    >
      <Table bordered dataSource={dataSource} columns={columns} pagination={false} />
    </Drawer>
  );
};

export default Form.create<any>({ name: 'CircleActiveDetailDrawer' })(
  forwardRef<any, any>(CircleActiveDetailDrawer)
);
