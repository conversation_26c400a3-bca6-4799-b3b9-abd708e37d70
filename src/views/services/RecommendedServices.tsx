/* eslint-disable no-return-assign */
import { getTableList } from '@app/action/tableList';
import { serviceApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import Form from '@components/business/RecommendedServicesForm';
import KeyForm from '@components/business/KeyRecommendedServicesForm';

import { A, Drawer, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils';
import {
  Button,
  Col,
  Dropdown,
  Icon,
  Divider,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Form as AForm,
  Input,
} from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class RecommendedServices extends React.Component<any, any> {
  formRef: any;

  keyFormRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      form: {
        visible: false,
        key: Date.now(),
        mtitle: '',
      },
      keyForm: {
        visible: false,
        key: Date.now(),
        mtitle: '',
      },
      onCount: 0,
      areaId: '',
      categoryList: [],
      areaList: [],
      ModalAlert: {
        visible: false,
        title: '',
        record: null,
      },
    };
  }

  componentDidMount() {
    this.getCategoryList();
    this.getAreaList();

    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      this.props.tableList.allData.recommend_list
    ) {
      console.log();
      this.setState({
        onCount: this.props.tableList.allData.on_show_count,
      });
    }
  }

  getAreaList = () => {
    api.getServiceAreaList().then((res: any) => {
      const areaList = res.data.area_tree.map((v: any) => {
        return {
          title: v.name,
          value: v.id,
          key: v.id,
        };
      });
      this.setState({ areaList, areaId: areaList[0].value });
      this.getData({ current: 1, size: 10 });
    });
  };

  getCategoryList = () => {
    api.getSimpleCategoryList().then((r: any) => {
      this.setState({
        categoryList: r.data.category_list,
      });
    });
  };

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getServiceRecommendList', 'recommend_list', { ...filter, ...overlap })
    );
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { areaId } = this.state;
    return { current, size, area_id: areaId };
  };

  handleSubmit = () => {
    const { ModalAlert } = this.state;
    if (ModalAlert.title === '') {
      message.error('请检查必填项');
      return;
    }

    this.setState({ loading: true });
    const area_id = this.state.areaList.filter((x: any) => x.value === this.state.areaId)[0].value;
    const body = {
      id: ModalAlert.record.id,
      recommend_title: ModalAlert.title,
      area_id: area_id,
      url: ModalAlert.record.url,
      ref_ids: '',
    };

    api
      .updateServiceRecommend(body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({
          loading: false,
          ModalAlert: {
            ...ModalAlert,
            visible: false,
          },
        });
      })
      .catch(() => this.setState({ loading: false }));
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const orderColumns: any = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              'service_recommend:update_sort'
            )(
              <A
                disabled={getSeq(i) === 1 || !record.status}
                className="sort-up"
                onClick={() => this.order(record, 0)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              'service_recommend:update_sort'
            )(
              <A
                disabled={getSeq(i) === this.state.onCount || !record.status}
                className="sort-down"
                onClick={() => this.order(record, 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 80,
      },
    ];

    const columns: any = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '推荐标题',
        key: 'recommend_title',
        dataIndex: 'recommend_title',
        width: 200,
      },
      {
        title: '应用名称',
        key: 'servcie_name',
        dataIndex: 'servcie_name',
        width: 150,
      },
      {
        title: '所属分类',
        key: 'servcie_class',
        dataIndex: 'servcie_class',
        width: 150,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: any) => <span>{text ? '已启用' : '未启用'}</span>,
        width: 80,
      },
      {
        title: '链接',
        key: 'url',
        dataIndex: 'url',
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              `service_recommend:update_status`
            )(<A onClick={() => this.changeEnabled(record)}>{record.status ? '下架' : '上架'}</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              `service_recommend:update`
            )(<A onClick={() => this.editRecord(record)}>编辑</A>)}

            {record.web_link_code != 'zlb' && (
              <>
                <Divider type="vertical" />
                {requirePerm(
                  this,
                  `service_recommend:delete${record.status ? 'xx' : ''}`
                )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
              </>
            )}
          </span>
        ),
        width: 200,
      },
    ];

    return orderColumns.concat(columns);
  };

  order = (record: any, sort_flag: number) => {
    setLoading(this, true);
    api
      .updateSortServiceRecommend({ sort_flag, id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  changeRecommend = (record: any) => {
    setLoading(this, true);
    api
      .updateAppServiceRecommend(record.recommended ? 'cancel_recommend' : 'do_recommend', {
        id: record.id,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  changeEnabled = (record: any) => {
    api.getServiceRecommendVipDetail({ area_id: this.state.areaId }).then((res: any) => {
      const data = res.data && res.data.recommend_service;
      if (data && Number(data.id) === record.id) {
        Modal.confirm({
          title: <p>该服务为重点推荐服务，下架将会同步清除该配置，是否下架？</p>,
          onOk: () => {
            setLoading(this, true);
            api
              .updateStatusServiceRecommend({ id: record.id, status: record.status ? '0' : '1' })
              .then(() => {
                message.success('操作成功');
                this.getData();
                setLoading(this, false);
              })
              .catch(() => setLoading(this, false));
          },
        });
      } else {
        setLoading(this, true);
        api
          .updateStatusServiceRecommend({ id: record.id, status: record.status ? '0' : '1' })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      }
    });
  };

  deleteRecord = (record: any) => {
    setLoading(this, true);
    api
      .deleteServiceRecommend({ id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  editRecord = (record: CommonObject = {}) => {
    if (record.web_link_code == 'zlb') {
      this.setState({
        ModalAlert: {
          visible: true,
          title: record.recommend_title,
          record,
        },
      });

      return;
    }

    if (!record.id) {
      this.setState({
        form: {
          visible: true,
          key: Date.now(),
          mtitle: `添加推荐服务-${
            this.state.areaList.filter((x: any) => x.value === this.state.areaId)[0].title
          }`,
        },
      });
    } else {
      this.setState({
        form: {
          visible: true,
          key: Date.now(),
          mtitle: `编辑推荐服务-${
            this.state.areaList.filter((x: any) => x.value === this.state.areaId)[0].title
          }`,
          id: record.id,
          category_id: record.servcie_class_id,
          recommend_title: record.recommend_title,
          ref_ids: record.servvice_id,
          url: record.url,
        },
      });
    }
  };

  editRecordKey = (record: CommonObject = {}) => {
    console.log(this.state.areaId);
    this.setState({
      keyForm: {
        visible: true,
        key: Date.now(),
        mtitle: `重点推荐服务配置-${
          this.state.areaList.filter((x: any) => x.value === this.state.areaId)[0].title
        }`,
        area_id: this.state.areaId,
      },
    });
  };

  areaChange = (v: any) => {
    this.setState(
      {
        areaId: v,
      },
      () => this.getData({ current: 1 })
    );
  };

  submitEnd = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
      loading: false,
    });
    this.getData();
  };

  submitEndKey = () => {
    this.setState({
      keyForm: {
        ...this.state.keyForm,
        visible: false,
      },
      loading: false,
    });
    this.getData();
  };

  setLoading = (loading: boolean) => {
    this.setState({ loading });
  };

  closeDrawer = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
    });
  };

  closeDrawerKey = () => {
    this.setState({
      keyForm: {
        ...this.state.keyForm,
        visible: false,
      },
    });
  };

  render() {
    const { form, loading, keyForm } = this.state;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={16}>
            <Button style={{ marginRight: 8 }} onClick={() => this.props.history.goBack()}>
              <Icon type="left-circle" /> 返回
            </Button>
            <Select
              value={this.state.areaId}
              onChange={this.areaChange}
              style={{ width: 120, marginRight: 8 }}
            >
              {this.state.areaList.map((v: any) => (
                <Select.Option key={v.value} value={v.value}>
                  {v.title}
                </Select.Option>
              ))}
            </Select>
            {requirePerm(
              this,
              'service_recommend:create'
            )(
              <Button onClick={() => this.editRecord()} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 添加推荐服务
              </Button>
            )}
            <span>该功能仅针对V7.3.0及以下版本生效。</span>
            {/* {requirePerm(
              this,
              'service_recommend:vip_save'
            )(
              <Button onClick={() => this.editRecordKey()} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 重点推荐服务设置
              </Button>
            )} */}
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getServiceRecommendList"
            index="recommend_list"
            rowKey="id"
            filter={this.getFilter()}
            columns={this.getColumns()}
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            title={form.mtitle}
            onClose={this.closeDrawer}
            onOk={() => this.formRef.doSubmit()}
          >
            <Form
              categoryList={this.state.categoryList}
              formContent={form}
              setLoading={this.setLoading}
              onEnd={this.submitEnd}
              wrappedComponentRef={(ref: any) => (this.formRef = ref)}
              areaList={this.state.areaList.filter((x: any) => x.value === this.state.areaId)}
            />
          </Drawer>

          <Drawer
            visible={keyForm.visible}
            skey={keyForm.key}
            title={keyForm.mtitle}
            onClose={this.closeDrawerKey}
            onOk={() => this.keyFormRef.doSubmit()}
          >
            <KeyForm
              formContent={keyForm}
              setLoading={this.setLoading}
              onEnd={this.submitEndKey}
              wrappedComponentRef={(ref: any) => (this.keyFormRef = ref)}
            />
          </Drawer>

          <Modal
            visible={this.state.ModalAlert.visible}
            key="sd"
            title={`页卡名称配置`}
            confirmLoading={loading}
            width="300px"
            onCancel={() =>
              this.setState({ ModalAlert: { ...this.state.ModalAlert, visible: false } })
            }
            onOk={this.handleSubmit}
          >
            <AForm {...formLayout}>
              <AForm.Item required={true} label="名称">
                <Input
                  value={this.state.ModalAlert.title}
                  placeholder="请输入名称"
                  onChange={(e: any) =>
                    this.setState({
                      ModalAlert: { ...this.state.ModalAlert, title: e.target.value },
                    })
                  }
                />
              </AForm.Item>
            </AForm>
          </Modal>
        </div>
      </>
    );
  }
}

export default RecommendedServices;
