import React from 'react';
import { Form } from 'antd';
import CreateGroupChat from '../views/operates/IM/components/CreateGroupChat';

// 模拟圈子数据
const mockCircleList = [
  { id: '1', name: '技术交流圈' },
  { id: '2', name: '产品讨论圈' },
  { id: '3', name: '设计分享圈' },
  { id: '4', name: '运营推广圈' },
  { id: '5', name: '创业投资圈' },
];

const TestComponent: React.FC = () => {
  const [form] = Form.useForm();
  const [visible, setVisible] = React.useState(true);

  const handleOk = (values: any) => {
    console.log('表单提交数据:', values);
    setVisible(false);
  };

  const handleCancel = () => {
    console.log('取消操作');
    setVisible(false);
  };

  return (
    <div>
      <h2>关联圈子搜索功能测试</h2>
      <p>测试要点：</p>
      <ul>
        <li>1. 点击关联圈子下拉框，应该显示搜索输入框</li>
        <li>2. 在搜索框中输入关键词，应该实时过滤圈子列表</li>
        <li>3. 搜索"技术"应该只显示"技术交流圈"</li>
        <li>4. 搜索"圈"应该显示所有圈子</li>
        <li>5. 搜索"不存在"应该显示"暂无匹配的圈子"</li>
        <li>6. 清空搜索框应该显示完整列表</li>
        <li>7. 点击选项应该能正确选择圈子</li>
      </ul>
      
      <CreateGroupChat
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        form={form}
        circleList={mockCircleList}
      />
    </div>
  );
};

export default TestComponent;
